#!/bin/bash

# 🚀 医院回访系统 Linux 自动部署脚本
# 包含字段录入历史记录功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y python3 python3-pip python3-venv git nginx curl
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
        sudo yum update -y
        sudo yum install -y python3 python3-pip git nginx curl
        sudo yum groupinstall -y "Development Tools"
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_success "系统依赖安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js..."
    
    if ! command -v node &> /dev/null; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
            sudo apt-get install -y nodejs
        elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
            sudo yum install -y nodejs npm
        fi
    else
        log_info "Node.js 已安装: $(node --version)"
    fi
    
    log_success "Node.js 安装完成"
}

# 克隆项目
clone_project() {
    log_info "克隆项目代码..."
    
    PROJECT_DIR="/opt/hospital-followup-system"
    
    if [[ -d $PROJECT_DIR ]]; then
        log_warning "项目目录已存在，正在更新..."
        cd $PROJECT_DIR
        git pull origin master
    else
        sudo git clone https://gitee.com/jiang-linlinlin/hospital-followup-system.git $PROJECT_DIR
        sudo chown -R $USER:$USER $PROJECT_DIR
    fi
    
    cd $PROJECT_DIR
    log_success "项目代码准备完成"
}

# 部署后端
deploy_backend() {
    log_info "部署后端服务..."
    
    cd $PROJECT_DIR/backend
    
    # 创建虚拟环境
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    
    # 安装依赖
    pip install -r requirements.txt
    
    # 初始化数据库
    python -c "
from app import create_app, db
app = create_app()
app.app_context().push()
db.create_all()
print('数据库初始化完成')
"
    
    # 创建管理员用户
    python -c "
from app import create_app, db
from app.models import User, Department
from werkzeug.security import generate_password_hash

app = create_app()
app.app_context().push()

# 创建默认科室
dept = Department.query.filter_by(name='系统管理').first()
if not dept:
    dept = Department(name='系统管理', description='系统管理科室')
    db.session.add(dept)
    db.session.commit()

# 创建管理员用户
admin = User.query.filter_by(username='admin').first()
if not admin:
    admin = User(
        username='admin',
        password_hash=generate_password_hash('admin123'),
        real_name='系统管理员',
        role='admin',
        department_id=dept.id
    )
    db.session.add(admin)
    db.session.commit()
    print('管理员用户创建完成')
else:
    print('管理员用户已存在')
"
    
    log_success "后端部署完成"
}

# 创建测试数据
create_test_data() {
    log_info "创建测试数据..."

    cd $PROJECT_DIR/backend
    source venv/bin/activate

    # 创建测试数据脚本
    python -c "
import sys
sys.path.append('.')
from app import create_app, db
from app.models import User, Department, PatientTemplate, PatientTemplateField, FollowupTemplate, TemplateField, Patient, FollowupRecord
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import json

app = create_app()
app.app_context().push()

print('开始创建测试数据...')

# 创建科室
departments_data = [
    {'name': '内科', 'description': '内科医疗服务'},
    {'name': '外科', 'description': '外科医疗服务'},
    {'name': '儿科', 'description': '儿童医疗服务'},
    {'name': '妇产科', 'description': '妇产科医疗服务'},
    {'name': '骨科', 'description': '骨科医疗服务'}
]

for dept_data in departments_data:
    dept = Department.query.filter_by(name=dept_data['name']).first()
    if not dept:
        dept = Department(**dept_data)
        db.session.add(dept)

db.session.commit()
print('科室数据创建完成')

# 创建用户
users_data = [
    {'username': 'doctor1', 'password': 'doctor123', 'real_name': '张医生', 'role': 'doctor', 'department': '内科'},
    {'username': 'doctor2', 'password': 'doctor123', 'real_name': '李医生', 'role': 'doctor', 'department': '外科'},
    {'username': 'nurse1', 'password': 'nurse123', 'real_name': '王护士', 'role': 'nurse', 'department': '儿科'},
    {'username': 'nurse2', 'password': 'nurse123', 'real_name': '赵护士', 'role': 'nurse', 'department': '妇产科'},
]

for user_data in users_data:
    user = User.query.filter_by(username=user_data['username']).first()
    if not user:
        dept = Department.query.filter_by(name=user_data['department']).first()
        user = User(
            username=user_data['username'],
            password_hash=generate_password_hash(user_data['password']),
            real_name=user_data['real_name'],
            role=user_data['role'],
            department_id=dept.id if dept else 1
        )
        db.session.add(user)

db.session.commit()
print('用户数据创建完成')

# 创建患者模板
patient_template = PatientTemplate.query.filter_by(name='标准患者信息模板').first()
if not patient_template:
    patient_template = PatientTemplate(
        name='标准患者信息模板',
        description='标准的患者信息录入模板',
        created_by=1
    )
    db.session.add(patient_template)
    db.session.commit()

    # 添加患者模板字段
    patient_fields = [
        {'field_name': 'name', 'field_label': '患者姓名', 'field_type': 'text', 'is_required': True, 'order_index': 1},
        {'field_name': 'gender', 'field_label': '性别', 'field_type': 'select', 'field_options': json.dumps(['男', '女']), 'is_required': True, 'order_index': 2},
        {'field_name': 'age', 'field_label': '年龄', 'field_type': 'number', 'is_required': True, 'order_index': 3},
        {'field_name': 'phone', 'field_label': '联系电话', 'field_type': 'text', 'is_required': True, 'order_index': 4},
        {'field_name': 'address', 'field_label': '家庭住址', 'field_type': 'textarea', 'is_required': False, 'order_index': 5},
        {'field_name': 'diagnosis', 'field_label': '诊断结果', 'field_type': 'textarea', 'is_required': True, 'order_index': 6},
        {'field_name': 'treatment', 'field_label': '治疗方案', 'field_type': 'textarea', 'is_required': True, 'order_index': 7},
    ]

    for field_data in patient_fields:
        field = PatientTemplateField(
            template_id=patient_template.id,
            **field_data
        )
        db.session.add(field)

db.session.commit()
print('患者模板创建完成')

# 创建回访模板
followup_template = FollowupTemplate.query.filter_by(name='标准回访模板').first()
if not followup_template:
    followup_template = FollowupTemplate(
        name='标准回访模板',
        description='标准的患者回访模板',
        created_by=1
    )
    db.session.add(followup_template)
    db.session.commit()

    # 添加回访模板字段
    followup_fields = [
        {'field_name': 'health_status', 'field_label': '健康状况', 'field_type': 'select', 'field_options': json.dumps(['很好', '良好', '一般', '较差', '很差']), 'is_required': True, 'order_index': 1},
        {'field_name': 'symptoms', 'field_label': '症状描述', 'field_type': 'textarea', 'is_required': False, 'order_index': 2},
        {'field_name': 'medication_compliance', 'field_label': '用药依从性', 'field_type': 'select', 'field_options': json.dumps(['完全按医嘱', '基本按医嘱', '偶尔忘记', '经常忘记', '未按医嘱']), 'is_required': True, 'order_index': 3},
        {'field_name': 'side_effects', 'field_label': '药物不良反应', 'field_type': 'textarea', 'is_required': False, 'order_index': 4},
        {'field_name': 'satisfaction', 'field_label': '满意度', 'field_type': 'select', 'field_options': json.dumps(['非常满意', '满意', '一般', '不满意', '非常不满意']), 'is_required': True, 'order_index': 5},
        {'field_name': 'suggestions', 'field_label': '意见建议', 'field_type': 'textarea', 'is_required': False, 'order_index': 6},
    ]

    for field_data in followup_fields:
        field = TemplateField(
            template_id=followup_template.id,
            **field_data
        )
        db.session.add(field)

db.session.commit()
print('回访模板创建完成')

print('测试数据创建完成！')
"

    log_success "测试数据创建完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端应用..."
    
    cd $PROJECT_DIR/frontend
    
    # 安装依赖
    npm install
    
    # 构建生产版本
    npm run build
    
    # 复制到nginx目录
    sudo mkdir -p /var/www/html
    sudo cp -r dist/* /var/www/html/
    
    log_success "前端构建完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."

    # 获取服务器IP
    SERVER_IP=$(curl -s ifconfig.me || echo "localhost")

    # 设置域名（可通过环境变量自定义）
    DOMAIN_NAME=${DOMAIN_NAME:-"fw.beimoyinhenlinlin.cn"}

    sudo tee /etc/nginx/sites-available/hospital-followup > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME $SERVER_IP localhost;

    # 前端静态文件
    location / {
        root /var/www/html;
        try_files \$uri \$uri/ /index.html;
        index index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 认证API代理
    location /auth/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/hospital-followup /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    
    # 重启nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log_success "Nginx配置完成"
}

# 配置系统服务
configure_service() {
    log_info "配置系统服务..."
    
    sudo tee /etc/systemd/system/hospital-followup.service > /dev/null <<EOF
[Unit]
Description=Hospital Followup System Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR/backend
Environment=PATH=$PROJECT_DIR/backend/venv/bin
ExecStart=$PROJECT_DIR/backend/venv/bin/python run.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    # 启动服务
    sudo systemctl daemon-reload
    sudo systemctl start hospital-followup
    sudo systemctl enable hospital-followup
    
    log_success "系统服务配置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查后端服务
    if sudo systemctl is-active --quiet hospital-followup; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        sudo systemctl status hospital-followup
        return 1
    fi
    
    # 检查nginx服务
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务启动失败"
        sudo systemctl status nginx
        return 1
    fi
    
    # 测试API
    sleep 5  # 等待服务完全启动
    if curl -s http://localhost:5001/auth/login > /dev/null; then
        log_success "API服务响应正常"
    else
        log_warning "API服务可能需要更多时间启动"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    SERVER_IP=$(curl -s ifconfig.me || echo "localhost")
    DOMAIN_NAME=${DOMAIN_NAME:-"fw.beimoyinhenlinlin.cn"}

    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📊 访问信息:"
    echo "  主域名: http://$DOMAIN_NAME"
    echo "  备用IP: http://$SERVER_IP"
    echo "  后端API: http://$DOMAIN_NAME/api"
    echo ""
    echo "🔑 默认账户:"
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    echo "🔧 管理命令:"
    echo "  查看后端日志: sudo journalctl -u hospital-followup -f"
    echo "  重启后端: sudo systemctl restart hospital-followup"
    echo "  重启Nginx: sudo systemctl restart nginx"
    echo ""
    echo "✨ 功能特性:"
    echo "  - 字段录入历史记录"
    echo "  - 智能输入建议"
    echo "  - 用户隔离的历史记录"
    echo "  - 患者信息管理"
    echo "  - 回访计划制定"
    echo "  - 数据导出功能"
    echo ""
    echo "💡 自定义域名:"
    echo "  设置环境变量 DOMAIN_NAME=your-domain.com 来使用自定义域名"
    echo ""
}

# 主函数
main() {
    echo "🚀 开始部署医院回访系统..."
    echo ""

    check_root
    detect_os
    install_dependencies
    install_nodejs
    clone_project
    deploy_backend
    create_test_data
    build_frontend
    configure_nginx
    configure_service
    verify_deployment
    show_deployment_info

    log_success "部署完成！"
}

# 运行主函数
main "$@"
