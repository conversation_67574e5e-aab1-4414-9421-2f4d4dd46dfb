# 可选依赖 - 扩展功能
# 根据需要安装: pip install -r requirements-optional.txt

# Excel文件处理 (如需直接生成Excel格式而非CSV)
openpyxl==3.1.2
xlsxwriter==3.1.9

# 数据分析和处理
pandas==2.1.3
numpy==1.25.2

# 高级统计功能
sqlalchemy-utils==0.41.1

# 图表生成 (如需在后端生成统计图表)
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 邮件发送 (如需邮件通知功能)
Flask-Mail==0.9.1

# 任务队列 (如需异步处理大量数据导出)
celery==5.3.4
redis==5.0.1

# 缓存 (如需缓存统计数据)
Flask-Caching==2.1.0

# 日志增强
structlog==23.2.0

# API文档生成
flask-restx==1.3.0
flasgger==*******

# 性能监控
flask-profiler==1.8.1

# 数据验证增强
cerberus==1.3.5
jsonschema==4.20.0
