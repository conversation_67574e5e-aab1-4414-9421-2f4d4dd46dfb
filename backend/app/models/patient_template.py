from datetime import datetime
from app import db
import json

class PatientTemplate(db.Model):
    """患者信息模板模型"""
    __tablename__ = 'patient_templates'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 所属科室
    department_id = db.<PERSON>umn(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    department = db.relationship('Department', backref='patient_templates')
    
    # 模板状态
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    is_default = db.Column(db.<PERSON><PERSON>, default=False)  # 是否为科室默认模板
    
    # 创建者
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    creator = db.relationship('User', backref='created_patient_templates')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 模板字段
    fields = db.relationship('PatientTemplateField', backref='template', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_fields_dict(self):
        """获取字段配置"""
        return [field.to_dict() for field in self.fields.order_by(PatientTemplateField.order)]
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name if self.creator else None,
            'fields': self.get_fields_dict(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<PatientTemplate {self.name}>'


class PatientTemplateField(db.Model):
    """患者信息模板字段模型"""
    __tablename__ = 'patient_template_fields'

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('patient_templates.id'), nullable=False)
    field_name = db.Column(db.String(50), nullable=False)
    field_label = db.Column(db.String(100), nullable=False)
    field_type = db.Column(db.String(20), nullable=False)
    field_config = db.Column(db.Text, nullable=True)
    order = db.Column(db.Integer, default=0)
    is_required = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_config(self):
        if self.field_config:
            return json.loads(self.field_config)
        return {}

    def set_config(self, config_dict):
        self.field_config = json.dumps(config_dict)

    def to_dict(self):
        return {
            'id': self.id,
            'field_name': self.field_name,
            'field_label': self.field_label,
            'field_type': self.field_type,
            'field_config': self.get_config(),
            'order': self.order,
            'is_required': self.is_required
        }

    def __repr__(self):
        return f'<PatientTemplateField {self.field_label}>'
