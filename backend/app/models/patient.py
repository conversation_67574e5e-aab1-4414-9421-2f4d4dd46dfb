from datetime import datetime
from app import db
import json

class Patient(db.Model):
    """患者模型（精简版，仅保留模板相关字段）"""
    __tablename__ = 'patients'
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.<PERSON>ey('patient_templates.id'), nullable=False)
    template = db.relationship('PatientTemplate', backref='patients')
    data = db.Column(db.Text, nullable=True)  # 存储所有动态字段内容（JSON字符串）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_data_dict(self):
        if self.data:
            return json.loads(self.data)
        return {}

    def set_data(self, data_dict):
        self.data = json.dumps(data_dict)

    def to_dict(self):
        """转换为字典，包含扁平化的字段用于前端显示"""
        data = self.get_data_dict()

        # 从动态数据中提取常用字段
        result = {
            'id': self.id,
            'template_id': self.template_id,
            'template': self.template.to_dict() if self.template else None,
            'data': data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,

            # 扁平化常用字段，优先从动态数据中获取
            'name': data.get('patient_name') or data.get('name') or f'患者{self.id}',
            'gender': data.get('patient_gender') or data.get('gender') or '-',
            'age': data.get('patient_age') or data.get('age') or '-',
            'phone': data.get('patient_phone') or data.get('phone') or '-',
            'medical_record_no': data.get('medical_record_no') or data.get('medical_record_number') or '-',
            'diagnosis': data.get('diagnosis') or data.get('primary_diagnosis') or '-',
            'department_name': self.template.department.name if self.template and self.template.department else '-',
            'doctor_name': data.get('doctor_name') or data.get('attending_doctor') or '-',
            'admission_date': data.get('admission_date') or '-',
            'discharge_date': data.get('discharge_date') or '-',
        }

        # 计算随访次数
        from app.models.followup_record import FollowupRecord
        followup_count = FollowupRecord.query.filter_by(patient_id=self.id).count()
        result['followup_count'] = followup_count

        return result

    def __repr__(self):
        return f'<Patient {self.id}>'
