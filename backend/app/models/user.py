from datetime import datetime
from app import db, bcrypt
from flask_jwt_extended import create_access_token

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    real_name = db.Column(db.String(50), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    
    # 角色: super_admin, dept_admin, user
    role = db.Column(db.String(20), nullable=False, default='user')
    
    # 所属科室
    department_id = db.Column(db.Integer, db.<PERSON>Key('departments.id'), nullable=True)
    department = db.relationship('Department', foreign_keys=[department_id], backref='users')
    
    # 状态
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        """验证密码"""
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def generate_token(self):
        """生成JWT token"""
        return create_access_token(identity=str(self.id))
    
    def is_super_admin(self):
        """是否为超级管理员"""
        return self.role == 'super_admin'
    
    def is_dept_admin(self):
        """是否为科室管理员"""
        return self.role == 'dept_admin'
    
    def can_manage_department(self, dept_id):
        """是否可以管理指定科室"""
        if self.is_super_admin():
            return True
        return self.is_dept_admin() and self.department_id == dept_id
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'role': self.role,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
