from datetime import datetime
from app import db
import json

class FollowupRecord(db.Model):
    """回访记录模型"""
    __tablename__ = 'followup_records'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 关联信息
    patient_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>ey('patients.id'), nullable=False)
    patient = db.relationship('Patient', backref='followup_records')
    template_id = db.Column(db.Integer, db.ForeignKey('followup_templates.id'), nullable=False)
    
    # 回访信息
    followup_date = db.Column(db.DateTime, nullable=False)  # 计划回访时间
    actual_date = db.Column(db.DateTime, nullable=True)  # 实际回访时间
    followup_method = db.Column(db.String(20), nullable=False)  # 回访方式
    
    # 回访状态: pending, completed, failed, cancelled
    status = db.Column(db.String(20), nullable=False, default='pending')
    
    # 回访结果
    result = db.Column(db.String(20), nullable=True)  # 回访结果: success, no_answer, refused, invalid_number
    notes = db.Column(db.Text, nullable=True)  # 备注
    
    # 执行人
    executor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    executor = db.relationship('User', backref='executed_followups')
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 回访数据
    followup_data = db.relationship('FollowupData', backref='record', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_data_dict(self):
        """获取回访数据"""
        data = {}
        for item in self.followup_data:
            data[item.field_name] = item.field_value
        return data
    
    def set_data(self, data_dict):
        """设置回访数据"""
        # 清除现有数据
        self.followup_data.delete()
        
        # 添加新数据
        for field_name, field_value in data_dict.items():
            data_item = FollowupData(
                field_name=field_name,
                field_value=str(field_value) if field_value is not None else None
            )
            self.followup_data.append(data_item)
    
    def is_overdue(self):
        """是否已过期"""
        if self.status == 'pending' and self.followup_date < datetime.utcnow():
            return True
        return False
    
    def get_patient_name(self):
        """获取患者姓名"""
        if self.patient:
            patient_data = self.patient.get_data_dict()
            return patient_data.get('patient_name') or patient_data.get('name') or f'患者{self.patient_id}'
        return None

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'patient_id': self.patient_id,
            'patient_name': self.get_patient_name(),
            'template_id': self.template_id,
            'template_name': self.template.name if self.template else None,
            'template': self.template.to_dict() if self.template else None,
            'followup_date': self.followup_date.isoformat() if self.followup_date else None,
            'actual_date': self.actual_date.isoformat() if self.actual_date else None,
            'followup_method': self.followup_method,
            'status': self.status,
            'result': self.result,
            'notes': self.notes,
            'executor_id': self.executor_id,
            'executor_name': self.executor.real_name if self.executor else None,
            'data': self.get_data_dict(),
            'is_overdue': self.is_overdue(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<FollowupRecord {self.id}>'


class FollowupData(db.Model):
    """回访数据模型"""
    __tablename__ = 'followup_data'
    
    id = db.Column(db.Integer, primary_key=True)
    record_id = db.Column(db.Integer, db.ForeignKey('followup_records.id'), nullable=False)
    
    # 字段信息
    field_name = db.Column(db.String(50), nullable=False)
    field_value = db.Column(db.Text, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'field_name': self.field_name,
            'field_value': self.field_value
        }
    
    def __repr__(self):
        return f'<FollowupData {self.field_name}>'
