from datetime import datetime
from app import db
import json

class FollowupTemplate(db.Model):
    """回访模板模型"""
    __tablename__ = 'followup_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 所属科室
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    
    # 回访方式: phone, sms, wechat, email, visit
    followup_method = db.Column(db.String(20), nullable=False, default='phone')
    
    # 回访时间配置 (JSON格式)
    # 例: {"days": [1, 3, 7, 30], "time": "09:00"}
    followup_schedule = db.Column(db.Text, nullable=True)
    
    # 模板状态
    is_active = db.Column(db.<PERSON>, default=True)
    is_default = db.Column(db.<PERSON>, default=False)  # 是否为科室默认模板
    
    # 创建者
    created_by = db.Column(db.Inte<PERSON>, db.<PERSON>('users.id'), nullable=False)
    creator = db.relationship('User', backref='created_templates')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 模板字段
    fields = db.relationship('TemplateField', backref='template', lazy='dynamic', cascade='all, delete-orphan')
    
    # 关联的回访记录
    followup_records = db.relationship('FollowupRecord', backref='template', lazy='dynamic')
    
    def get_schedule(self):
        """获取回访时间配置"""
        if self.followup_schedule:
            return json.loads(self.followup_schedule)
        return {"days": [1, 3, 7], "time": "09:00"}
    
    def set_schedule(self, schedule_dict):
        """设置回访时间配置"""
        self.followup_schedule = json.dumps(schedule_dict)
    
    def get_fields_dict(self):
        """获取字段配置"""
        return [field.to_dict() for field in self.fields.order_by(TemplateField.order)]
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'followup_method': self.followup_method,
            'followup_schedule': self.get_schedule(),
            'is_active': self.is_active,
            'is_default': self.is_default,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name if self.creator else None,
            'fields': self.get_fields_dict(),
            'records_count': self.followup_records.count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<FollowupTemplate {self.name}>'


class TemplateField(db.Model):
    """模板字段模型"""
    __tablename__ = 'template_fields'

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('followup_templates.id'), nullable=False)
    field_name = db.Column(db.String(50), nullable=False)
    field_label = db.Column(db.String(100), nullable=False)
    field_type = db.Column(db.String(20), nullable=False)
    field_config = db.Column(db.Text, nullable=True)
    order = db.Column(db.Integer, default=0)
    is_required = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_config(self):
        if self.field_config:
            return json.loads(self.field_config)
        return {}

    def set_config(self, config_dict):
        self.field_config = json.dumps(config_dict)

    def to_dict(self):
        return {
            'id': self.id,
            'field_name': self.field_name,
            'field_label': self.field_label,
            'field_type': self.field_type,
            'field_config': self.get_config(),
            'order': self.order,
            'is_required': self.is_required
        }

    def __repr__(self):
        return f'<TemplateField {self.field_label}>'
