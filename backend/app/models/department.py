from datetime import datetime
from app import db

class Department(db.Model):
    """科室模型"""
    __tablename__ = 'departments'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(20), nullable=False, unique=True)  # 科室编码
    description = db.Column(db.Text, nullable=True)
    
    # 科室负责人
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    manager = db.relationship('User', foreign_keys=[manager_id], post_update=True)
    
    # 联系信息
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    address = db.Column(db.String(200), nullable=True)
    
    # 状态
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的回访模板
    followup_templates = db.relationship('FollowupTemplate', backref='department', lazy='dynamic')
    
    def get_active_templates(self):
        """获取活跃的回访模板"""
        return self.followup_templates.filter_by(is_active=True).all()
    
    def get_users_count(self):
        """获取科室用户数量"""
        return len(self.users)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'manager_id': self.manager_id,
            'manager_name': self.manager.real_name if self.manager else None,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'is_active': self.is_active,
            'users_count': self.get_users_count(),
            'templates_count': self.followup_templates.count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Department {self.name}>'
