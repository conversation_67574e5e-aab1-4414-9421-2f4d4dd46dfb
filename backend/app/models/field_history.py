from datetime import datetime, timed<PERSON>ta
from app import db
from sqlalchemy import Index

class FieldHistory(db.Model):
    """字段录入历史记录"""
    __tablename__ = 'field_history'
    
    id = db.Column(db.Integer, primary_key=True)
    field_name = db.Column(db.String(100), nullable=False)  # 字段名称
    field_value = db.Column(db.Text, nullable=False)  # 字段值
    template_type = db.Column(db.String(50), nullable=False)  # 模板类型: patient, followup
    template_id = db.Column(db.Integer)  # 模板ID（可选）
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 录入用户
    usage_count = db.Column(db.Integer, default=1)  # 使用次数
    last_used = db.Column(db.DateTime, default=datetime.utcnow)  # 最后使用时间
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    user = db.relationship('User', backref='field_histories')
    
    # 索引 - MySQL兼容版本
    __table_args__ = (
        Index('idx_field_history_lookup', 'field_name', 'template_type', 'user_id'),
        # MySQL不支持TEXT字段的完整索引，使用前缀索引
        Index('idx_field_history_value_prefix', 'field_value', mysql_length=100),
        Index('idx_field_history_usage', 'usage_count', 'last_used'),
    )
    
    @classmethod
    def record_usage(cls, field_name, field_value, template_type, template_id, user_id):
        """记录字段使用历史"""
        if not field_value or not field_value.strip():
            return None
            
        # 查找是否已存在相同的记录
        existing = cls.query.filter_by(
            field_name=field_name,
            field_value=field_value.strip(),
            template_type=template_type,
            user_id=user_id
        ).first()
        
        if existing:
            # 更新使用次数和最后使用时间
            existing.usage_count += 1
            existing.last_used = datetime.utcnow()
            if template_id:
                existing.template_id = template_id
        else:
            # 创建新记录
            existing = cls(
                field_name=field_name,
                field_value=field_value.strip(),
                template_type=template_type,
                template_id=template_id,
                user_id=user_id,
                usage_count=1,
                last_used=datetime.utcnow()
            )
            db.session.add(existing)
        
        return existing
    
    @classmethod
    def get_suggestions(cls, field_name, template_type, user_id, query='', limit=10):
        """获取字段建议"""
        base_query = cls.query.filter_by(
            field_name=field_name,
            template_type=template_type,
            user_id=user_id
        )
        
        if query:
            base_query = base_query.filter(
                cls.field_value.ilike(f'%{query}%')
            )
        
        # 按使用次数和最后使用时间排序
        suggestions = base_query.order_by(
            cls.usage_count.desc(),
            cls.last_used.desc()
        ).limit(limit).all()
        
        return [item.field_value for item in suggestions]
    
    @classmethod
    def cleanup_old_records(cls, days=90, max_records_per_field=50):
        """清理旧记录"""
        from sqlalchemy import func
        
        # 删除超过指定天数且使用次数少的记录
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        old_records = cls.query.filter(
            cls.last_used < cutoff_date,
            cls.usage_count <= 2
        ).all()
        
        for record in old_records:
            db.session.delete(record)
        
        # 为每个字段保留最多指定数量的记录
        fields = db.session.query(
            cls.field_name, 
            cls.template_type, 
            cls.user_id
        ).distinct().all()
        
        for field_name, template_type, user_id in fields:
            records = cls.query.filter_by(
                field_name=field_name,
                template_type=template_type,
                user_id=user_id
            ).order_by(
                cls.usage_count.desc(),
                cls.last_used.desc()
            ).all()
            
            if len(records) > max_records_per_field:
                for record in records[max_records_per_field:]:
                    db.session.delete(record)
    
    def to_dict(self):
        return {
            'id': self.id,
            'field_name': self.field_name,
            'field_value': self.field_value,
            'template_type': self.template_type,
            'template_id': self.template_id,
            'usage_count': self.usage_count,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<FieldHistory {self.field_name}: {self.field_value[:20]}...>'
