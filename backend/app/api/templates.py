from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, FollowupTemplate, TemplateField, Department
from app.services.template_generator import template_generator
from . import api_bp

@api_bp.route('/templates', methods=['GET'])
@jwt_required()
def get_templates():
    """获取回访模板列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    department_id = request.args.get('department_id', type=int)
    is_active = request.args.get('is_active', type=bool)
    
    query = FollowupTemplate.query
    
    # 权限控制
    if current_user.is_super_admin():
        # 超级管理员可以查看所有模板
        pass
    else:
        # 其他用户只能查看本科室模板
        query = query.filter_by(department_id=current_user.department_id)
    
    # 筛选条件
    if department_id and current_user.is_super_admin():
        query = query.filter_by(department_id=department_id)
    
    if is_active is not None:
        query = query.filter_by(is_active=is_active)
    
    pagination = query.order_by(FollowupTemplate.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    templates = pagination.items
    
    return jsonify({
        'templates': [template.to_dict() for template in templates],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/templates', methods=['POST'])
@jwt_required()
def create_template():
    """创建回访模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 权限检查：只有科室管理员和超级管理员可以创建模板
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400

    # 如果没有提供回访方式，根据模板名称自动生成
    followup_method = data.get('followup_method')
    if not followup_method:
        followup_method = template_generator.get_suggested_followup_method(data['name'])

    # 创建模板
    template = FollowupTemplate(
        name=data['name'],
        description=data.get('description'),
        department_id=data.get('department_id', current_user.department_id),
        followup_method=followup_method,
        is_active=data.get('is_active', True),
        is_default=data.get('is_default', False),
        created_by=current_user.id
    )
    
    # 非超级管理员只能在自己科室创建模板
    if not current_user.is_super_admin():
        template.department_id = current_user.department_id
    
    # 设置回访时间配置
    if data.get('followup_schedule'):
        template.set_schedule(data['followup_schedule'])
    else:
        # 根据模板名称自动生成回访时间安排
        suggested_schedule = template_generator.get_suggested_followup_schedule(data['name'])
        template.set_schedule(suggested_schedule)

    db.session.add(template)
    db.session.flush()  # 获取模板ID

    # 创建模板字段
    fields_data = data.get('fields', [])

    # 如果没有提供字段配置，则根据模板名称自动生成
    if not fields_data:
        fields_data = template_generator.generate_followup_template_fields(template.name)

    # 验证字段名是否重复
    field_names = [field['field_name'] for field in fields_data]
    if len(field_names) != len(set(field_names)):
        return jsonify({'error': '字段名不能重复'}), 400

    for field_data in fields_data:
        field = TemplateField(
            template_id=template.id,
            field_name=field_data['field_name'],
            field_label=field_data['field_label'],
            field_type=field_data['field_type'],
            order=field_data.get('order', 0),
            is_required=field_data.get('is_required', False)
        )
        
        if field_data.get('field_config'):
            field.set_config(field_data['field_config'])
        
        db.session.add(field)
    
    db.session.commit()
    
    return jsonify({
        'message': '模板创建成功',
        'template': template.to_dict()
    }), 201

@api_bp.route('/templates/<int:template_id>', methods=['GET'])
@jwt_required()
def get_template(template_id):
    """获取模板详情"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = FollowupTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    return jsonify({'template': template.to_dict()})

@api_bp.route('/templates/<int:template_id>', methods=['PUT'])
@jwt_required()
def update_template(template_id):
    """更新模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = FollowupTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 更新基本信息
    if 'name' in data:
        template.name = data['name']
    if 'description' in data:
        template.description = data['description']
    if 'followup_method' in data:
        template.followup_method = data['followup_method']
    if 'is_active' in data:
        template.is_active = data['is_active']
    if 'is_default' in data:
        template.is_default = data['is_default']
    
    # 更新回访时间配置
    if 'followup_schedule' in data:
        template.set_schedule(data['followup_schedule'])
    
    # 只有超级管理员可以修改科室
    if current_user.is_super_admin() and 'department_id' in data:
        template.department_id = data['department_id']
    
    # 更新字段
    if 'fields' in data:
        # 验证字段名是否重复
        field_names = [field['field_name'] for field in data['fields']]
        if len(field_names) != len(set(field_names)):
            return jsonify({'error': '字段名不能重复'}), 400

        # 删除现有字段
        template.fields.delete()

        # 添加新字段
        for field_data in data['fields']:
            field = TemplateField(
                template_id=template.id,
                field_name=field_data['field_name'],
                field_label=field_data['field_label'],
                field_type=field_data['field_type'],
                order=field_data.get('order', 0),
                is_required=field_data.get('is_required', False)
            )
            
            if field_data.get('field_config'):
                field.set_config(field_data['field_config'])
            
            db.session.add(field)
    
    db.session.commit()
    
    return jsonify({
        'message': '模板更新成功',
        'template': template.to_dict()
    })

@api_bp.route('/templates/preview-fields', methods=['POST'])
@jwt_required()
def preview_followup_template_fields():
    """预览根据模板名称自动生成的字段和配置"""
    try:
        user_id = int(get_jwt_identity())
        current_user = User.query.get(user_id)

        if not current_user:
            return jsonify({'error': '用户不存在'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        template_name = data.get('name')

        if not template_name:
            return jsonify({'error': '模板名称不能为空'}), 400

        # 生成预览字段
        fields = template_generator.generate_followup_template_fields(template_name)

        # 生成建议的回访配置
        suggested_schedule = template_generator.get_suggested_followup_schedule(template_name)
        suggested_method = template_generator.get_suggested_followup_method(template_name)

        return jsonify({
            'fields': fields,
            'total_fields': len(fields),
            'suggested_followup_method': suggested_method,
            'suggested_followup_schedule': suggested_schedule
        })
    except Exception as e:
        print(f"预览回访模板字段错误: {str(e)}")
        return jsonify({'error': f'预览失败: {str(e)}'}), 500

@api_bp.route('/templates/<int:template_id>', methods=['DELETE'])
@jwt_required()
def delete_template(template_id):
    """删除模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = FollowupTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    # 检查是否有关联的回访记录
    if template.followup_records.count() > 0:
        return jsonify({'error': '该模板有回访记录，无法删除'}), 400
    
    db.session.delete(template)
    db.session.commit()
    
    return jsonify({'message': '模板删除成功'})

@api_bp.route('/templates/<int:template_id>/copy', methods=['POST'])
@jwt_required()
def copy_template(template_id):
    """复制模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = FollowupTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    new_name = data.get('name', f"{template.name}_副本")
    
    # 创建新模板
    new_template = FollowupTemplate(
        name=new_name,
        description=template.description,
        department_id=current_user.department_id,
        followup_method=template.followup_method,
        followup_schedule=template.followup_schedule,
        is_active=True,
        is_default=False,
        created_by=current_user.id
    )
    
    db.session.add(new_template)
    db.session.flush()
    
    # 复制字段
    for field in template.fields:
        new_field = TemplateField(
            template_id=new_template.id,
            field_name=field.field_name,
            field_label=field.field_label,
            field_type=field.field_type,
            field_config=field.field_config,
            order=field.order,
            is_required=field.is_required
        )
        db.session.add(new_field)
    
    db.session.commit()
    
    return jsonify({
        'message': '模板复制成功',
        'template': new_template.to_dict()
    }), 201
