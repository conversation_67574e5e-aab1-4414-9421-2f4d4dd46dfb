from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, Department
from . import api_bp

@api_bp.route('/departments', methods=['GET'])
@jwt_required()
def get_departments():
    """获取科室列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    is_active = request.args.get('is_active', type=bool)
    
    query = Department.query
    
    # 筛选条件
    if is_active is not None:
        query = query.filter_by(is_active=is_active)
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    departments = pagination.items
    
    return jsonify({
        'departments': [dept.to_dict() for dept in departments],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/departments', methods=['POST'])
@jwt_required()
def create_department():
    """创建科室"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user or not current_user.is_super_admin():
        return jsonify({'error': '只有超级管理员可以创建科室'}), 403
    
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['name', 'code']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400
    
    # 检查科室名称和编码是否已存在
    if Department.query.filter_by(name=data['name']).first():
        return jsonify({'error': '科室名称已存在'}), 400
    
    if Department.query.filter_by(code=data['code']).first():
        return jsonify({'error': '科室编码已存在'}), 400
    
    # 创建科室
    department = Department(
        name=data['name'],
        code=data['code'],
        description=data.get('description'),
        manager_id=data.get('manager_id'),
        phone=data.get('phone'),
        email=data.get('email'),
        address=data.get('address'),
        is_active=data.get('is_active', True)
    )
    
    db.session.add(department)
    db.session.commit()
    
    return jsonify({
        'message': '科室创建成功',
        'department': department.to_dict()
    }), 201

@api_bp.route('/departments/<int:dept_id>', methods=['GET'])
@jwt_required()
def get_department(dept_id):
    """获取科室详情"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    department = Department.query.get(dept_id)
    
    if not current_user or not department:
        return jsonify({'error': '科室不存在'}), 404
    
    return jsonify({'department': department.to_dict()})

@api_bp.route('/departments/<int:dept_id>', methods=['PUT'])
@jwt_required()
def update_department(dept_id):
    """更新科室"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    department = Department.query.get(dept_id)
    
    if not current_user or not department:
        return jsonify({'error': '科室不存在'}), 404
    
    # 权限检查
    if not current_user.can_manage_department(dept_id):
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 更新字段
    if 'name' in data:
        existing_dept = Department.query.filter_by(name=data['name']).first()
        if existing_dept and existing_dept.id != department.id:
            return jsonify({'error': '科室名称已存在'}), 400
        department.name = data['name']
    
    if 'code' in data:
        existing_dept = Department.query.filter_by(code=data['code']).first()
        if existing_dept and existing_dept.id != department.id:
            return jsonify({'error': '科室编码已存在'}), 400
        department.code = data['code']
    
    if 'description' in data:
        department.description = data['description']
    if 'phone' in data:
        department.phone = data['phone']
    if 'email' in data:
        department.email = data['email']
    if 'address' in data:
        department.address = data['address']
    
    # 只有超级管理员可以修改负责人和状态
    if current_user.is_super_admin():
        if 'manager_id' in data:
            department.manager_id = data['manager_id']
        if 'is_active' in data:
            department.is_active = data['is_active']
    
    db.session.commit()
    
    return jsonify({
        'message': '科室更新成功',
        'department': department.to_dict()
    })

@api_bp.route('/departments/<int:dept_id>', methods=['DELETE'])
@jwt_required()
def delete_department(dept_id):
    """删除科室"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    department = Department.query.get(dept_id)
    
    if not current_user or not department:
        return jsonify({'error': '科室不存在'}), 404
    
    # 只有超级管理员可以删除科室
    if not current_user.is_super_admin():
        return jsonify({'error': '只有超级管理员可以删除科室'}), 403
    
    # 检查是否有关联的用户
    if department.users:
        return jsonify({'error': '该科室下还有用户，无法删除'}), 400
    
    # 检查是否有关联的患者
    if department.patients:
        return jsonify({'error': '该科室下还有患者，无法删除'}), 400
    
    db.session.delete(department)
    db.session.commit()
    
    return jsonify({'message': '科室删除成功'})

@api_bp.route('/departments/<int:dept_id>/users', methods=['GET'])
@jwt_required()
def get_department_users(dept_id):
    """获取科室用户列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    department = Department.query.get(dept_id)
    
    if not current_user or not department:
        return jsonify({'error': '科室不存在'}), 404
    
    # 权限检查
    if not current_user.can_manage_department(dept_id):
        return jsonify({'error': '权限不足'}), 403
    
    users = User.query.filter_by(department_id=dept_id).all()
    
    return jsonify({
        'users': [user.to_dict() for user in users],
        'total': len(users)
    })

@api_bp.route('/departments/<int:dept_id>/templates', methods=['GET'])
@jwt_required()
def get_department_templates(dept_id):
    """获取科室回访模板列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    department = Department.query.get(dept_id)
    
    if not current_user or not department:
        return jsonify({'error': '科室不存在'}), 404
    
    # 权限检查
    if not current_user.can_manage_department(dept_id):
        return jsonify({'error': '权限不足'}), 403
    
    templates = department.get_active_templates()
    
    return jsonify({
        'templates': [template.to_dict() for template in templates],
        'total': len(templates)
    })
