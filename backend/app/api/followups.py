from flask import request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
from app import db
from app.models import User, FollowupRecord, FollowupTemplate, Patient, PatientTemplate, TemplateField, FollowupData
from . import api_bp
import json
import io
import csv

@api_bp.route('/followups', methods=['GET'])
@jwt_required()
def get_followups():
    """获取回访记录列表（只返回模板和动态字段数据）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    status = request.args.get('status')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search', '', type=str)

    query = FollowupRecord.query

    # 科室权限控制
    if current_user.is_super_admin():
        # 超级管理员可以看到所有随访记录
        pass
    else:
        # 普通用户和科室管理员只能看到自己科室的随访记录
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        # 通过随访模版的科室ID来过滤随访记录
        query = query.join(FollowupTemplate).filter(FollowupTemplate.department_id == current_user.department_id)

    # 患者搜索
    if search:
        # 通过患者表的数据字段搜索患者姓名
        from app.models.patient import Patient
        search_pattern = f'%{search}%'

        # 如果已经join了Patient表（由于科室权限控制），则不需要再次join
        if current_user.is_super_admin():
            query = query.join(Patient)

        # 使用JSON搜索功能搜索患者姓名
        query = query.filter(
            db.or_(
                Patient.data.like(search_pattern),  # 通用搜索
                # 使用JSON_EXTRACT搜索特定字段（如果数据库支持）
                db.text("JSON_EXTRACT(patients.data, '$.patient_name') LIKE :search_pattern").params(search_pattern=search_pattern),
                db.text("JSON_EXTRACT(patients.data, '$.name') LIKE :search_pattern").params(search_pattern=search_pattern)
            )
        )

    # 状态筛选
    if status:
        query = query.filter(FollowupRecord.status == status)

    # 日期筛选
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FollowupRecord.followup_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # 包含整天，所以加上23:59:59
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(FollowupRecord.followup_date <= date_to_obj)
        except ValueError:
            pass

    pagination = query.order_by(FollowupRecord.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    followups = pagination.items
    return jsonify({
        'followups': [f.to_dict() for f in followups],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/followups', methods=['POST'])
@jwt_required()
def create_followup():
    """创建回访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ['template_id', 'patient_id', 'followup_date']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400

    # 验证患者和模版是否存在
    patient = Patient.query.get(data['patient_id'])
    template = FollowupTemplate.query.get(data['template_id'])

    if not patient:
        return jsonify({'error': '患者不存在'}), 404
    if not template:
        return jsonify({'error': '随访模版不存在'}), 404

    # 权限检查：非超级管理员只能创建自己科室的随访记录
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403

        # 检查随访模版是否属于用户科室
        if template.department_id != current_user.department_id:
            return jsonify({'error': '无权使用该科室的随访模版'}), 403

        # 检查患者是否属于用户科室（通过患者模版）
        if patient.template.department_id != current_user.department_id:
            return jsonify({'error': '无权为该科室的患者创建随访记录'}), 403

    # 解析随访时间
    try:
        followup_date = datetime.fromisoformat(data['followup_date'].replace('Z', '+00:00'))
    except ValueError:
        return jsonify({'error': '随访时间格式错误'}), 400

    # 创建回访记录
    followup = FollowupRecord(
        patient_id=data['patient_id'],
        template_id=data['template_id'],
        followup_date=followup_date,
        followup_method=template.followup_method,
        status=data.get('status', 'pending'),
        result=data.get('result'),
        notes=data.get('notes'),
        executor_id=data.get('executor_id')
    )

    db.session.add(followup)
    db.session.flush()  # 获取ID

    # 设置动态字段数据
    if data.get('data'):
        followup.set_data(data['data'])

    db.session.commit()

    return jsonify({
        'message': '回访记录创建成功',
        'followup': followup.to_dict()
    }), 201

@api_bp.route('/followups/<int:followup_id>', methods=['GET'])
@jwt_required()
def get_followup(followup_id):
    """获取回访记录详情"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    followup = FollowupRecord.query.get(followup_id)

    if not current_user or not followup:
        return jsonify({'error': '回访记录不存在'}), 404

    return jsonify({'followup': followup.to_dict()})

@api_bp.route('/followups/<int:followup_id>', methods=['PUT'])
@jwt_required()
def update_followup(followup_id):
    """更新回访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    followup = FollowupRecord.query.get(followup_id)

    if not current_user or not followup:
        return jsonify({'error': '回访记录不存在'}), 404

    # 权限检查：非超级管理员只能更新自己科室的随访记录
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if followup.template.department_id != current_user.department_id:
            return jsonify({'error': '无权更新该科室的随访记录'}), 403

    data = request.get_json()

    # 更新基本字段
    if 'followup_date' in data:
        try:
            followup.followup_date = datetime.fromisoformat(data['followup_date'].replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'error': '随访时间格式错误'}), 400

    if 'actual_date' in data and data['actual_date']:
        try:
            followup.actual_date = datetime.fromisoformat(data['actual_date'].replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'error': '实际随访时间格式错误'}), 400

    if 'status' in data:
        followup.status = data['status']
    if 'result' in data:
        followup.result = data['result']
    if 'notes' in data:
        followup.notes = data['notes']
    if 'executor_id' in data:
        followup.executor_id = data['executor_id']

    # 更新动态字段数据
    if 'data' in data:
        followup.set_data(data['data'])

    db.session.commit()
    return jsonify({'message': '回访记录更新成功'})

@api_bp.route('/followups/<int:followup_id>', methods=['DELETE'])
@jwt_required()
def delete_followup(followup_id):
    """删除回访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    followup = FollowupRecord.query.get(followup_id)
    if not current_user or not followup:
        return jsonify({'error': '回访记录不存在'}), 404

    # 权限检查：非超级管理员只能删除自己科室的随访记录
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if followup.template.department_id != current_user.department_id:
            return jsonify({'error': '无权删除该科室的随访记录'}), 403

    db.session.delete(followup)
    db.session.commit()
    return jsonify({'message': '回访记录删除成功'})

@api_bp.route('/followups/batch-create-by-template', methods=['POST'])
@jwt_required()
def batch_create_followups_by_template():
    """批量创建回访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()

    # 验证必填字段
    required_fields = ['patient_template_id', 'followup_template_id', 'days_after']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400

    patient_template_id = data['patient_template_id']
    followup_template_id = data['followup_template_id']
    days_after = data['days_after']

    # 验证模板是否存在
    patient_template = PatientTemplate.query.get(patient_template_id)
    followup_template = FollowupTemplate.query.get(followup_template_id)

    if not patient_template:
        return jsonify({'error': '患者模板不存在'}), 404
    if not followup_template:
        return jsonify({'error': '回访模板不存在'}), 404

    # 权限检查：非超级管理员只能操作自己科室的模板
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if patient_template.department_id != current_user.department_id:
            return jsonify({'error': '无权操作该科室的患者模板'}), 403
        if followup_template.department_id != current_user.department_id:
            return jsonify({'error': '无权操作该科室的回访模板'}), 403

    # 查询使用指定患者模板录入的患者
    patients = Patient.query.filter_by(template_id=patient_template_id).all()

    if not patients:
        return jsonify({'error': '未找到使用该患者模板录入的患者'}), 400

    created_count = 0
    skipped_count = 0
    errors = []

    for patient in patients:
        try:
            # 检查是否已存在回访记录
            existing_followup = FollowupRecord.query.filter_by(
                patient_id=patient.id,
                template_id=followup_template_id
            ).first()

            if existing_followup:
                skipped_count += 1
                continue

            # 计算回访日期（从患者创建日期开始计算）
            followup_date = patient.created_at + timedelta(days=days_after)

            # 创建回访记录
            followup_record = FollowupRecord(
                patient_id=patient.id,
                template_id=followup_template_id,
                followup_date=followup_date,
                followup_method=followup_template.followup_method or '电话',
                status='pending'
            )

            db.session.add(followup_record)
            created_count += 1

        except Exception as e:
            errors.append(f'患者{patient.id}创建失败: {str(e)}')

    try:
        db.session.commit()

        result = {
            'message': '批量创建回访记录完成',
            'created_count': created_count,
            'skipped_count': skipped_count,
            'total_patients': len(patients)
        }

        if errors:
            result['errors'] = errors

        return jsonify(result)

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'批量创建失败: {str(e)}'}), 500

@api_bp.route('/followups/batch-preview', methods=['POST'])
@jwt_required()
def batch_preview_followups():
    """预览批量创建回访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()
    patient_template_id = data.get('patient_template_id')
    followup_template_id = data.get('followup_template_id')

    if not patient_template_id:
        return jsonify({'error': 'patient_template_id不能为空'}), 400

    # 验证模板是否存在
    patient_template = PatientTemplate.query.get(patient_template_id)
    if not patient_template:
        return jsonify({'error': '患者模板不存在'}), 404

    # 如果提供了回访模板ID，也验证一下
    followup_template = None
    if followup_template_id:
        followup_template = FollowupTemplate.query.get(followup_template_id)
        if not followup_template:
            return jsonify({'error': '回访模板不存在'}), 404

    # 权限检查
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if patient_template.department_id != current_user.department_id:
            return jsonify({'error': '无权操作该科室的患者模板'}), 403
        if followup_template and followup_template.department_id != current_user.department_id:
            return jsonify({'error': '无权操作该科室的回访模板'}), 403

    # 查询使用指定患者模板录入的患者
    patients = Patient.query.filter_by(template_id=patient_template_id).all()

    # 统计信息
    total_patients = len(patients)
    patients_with_followup = 0
    patients_without_followup = 0

    patient_list = []
    for patient in patients:
        patient_data = patient.to_dict()

        # 检查是否已有指定回访模板的回访记录（如果提供了回访模板ID）
        if followup_template_id:
            has_followup = FollowupRecord.query.filter_by(
                patient_id=patient.id,
                template_id=followup_template_id
            ).first() is not None
        else:
            # 如果没有提供回访模板ID，检查是否有任何回访记录
            has_followup = FollowupRecord.query.filter_by(patient_id=patient.id).first() is not None

        patient_data['has_followup'] = has_followup

        if has_followup:
            patients_with_followup += 1
        else:
            patients_without_followup += 1

        patient_list.append(patient_data)

    result = {
        'patient_template': patient_template.to_dict(),
        'total_patients': total_patients,
        'patients_with_followup': patients_with_followup,
        'patients_without_followup': patients_without_followup,
        'patients': patient_list[:10]  # 只返回前10个患者作为预览
    }

    if followup_template:
        result['followup_template'] = followup_template.to_dict()

    return jsonify(result)

@api_bp.route('/followups/<int:followup_id>/execute', methods=['POST'])
@jwt_required()
def execute_followup(followup_id):
    """执行回访"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    followup = FollowupRecord.query.get(followup_id)

    if not current_user or not followup:
        return jsonify({'error': '回访记录不存在'}), 404

    data = request.get_json()

    # 更新回访记录
    followup.actual_date = datetime.utcnow()
    followup.status = 'completed'
    followup.result = data.get('result', 'success')
    followup.notes = data.get('notes', '')
    followup.executor_id = current_user.id

    # 更新动态字段数据
    if data.get('data'):
        followup.set_data(data['data'])

    db.session.commit()

    return jsonify({
        'message': '回访执行成功',
        'followup': followup.to_dict()
    })



@api_bp.route('/followups/pending', methods=['GET'])
@jwt_required()
def get_pending_followups():
    """获取待处理的随访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取今天和未来7天的待处理随访
    today = datetime.utcnow().date()
    next_week = today + timedelta(days=7)

    query = FollowupRecord.query.filter(
        FollowupRecord.status == 'pending',
        FollowupRecord.followup_date >= today,
        FollowupRecord.followup_date <= next_week
    ).order_by(FollowupRecord.followup_date)

    followups = query.all()

    return jsonify({
        'followups': [f.to_dict() for f in followups],
        'total': len(followups)
    })

@api_bp.route('/followups/overdue', methods=['GET'])
@jwt_required()
def get_overdue_followups():
    """获取逾期的随访记录"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取逾期的待处理随访
    now = datetime.utcnow()

    query = FollowupRecord.query.filter(
        FollowupRecord.status == 'pending',
        FollowupRecord.followup_date < now
    ).order_by(FollowupRecord.followup_date.desc())

    followups = query.all()

    return jsonify({
        'followups': [f.to_dict() for f in followups],
        'total': len(followups)
    })

@api_bp.route('/followups/stats', methods=['GET'])
@jwt_required()
def get_followup_stats():
    """获取回访统计信息"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 基础查询
    base_query = FollowupRecord.query

    # 科室权限控制
    if current_user.is_super_admin():
        # 超级管理员可以看到所有随访记录
        pass
    else:
        # 普通用户和科室管理员只能看到自己科室的随访记录
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        # 通过随访模版的科室ID来过滤随访记录
        base_query = base_query.join(FollowupTemplate).filter(FollowupTemplate.department_id == current_user.department_id)

    # 统计各种状态的回访数量
    total_followups = base_query.count()
    completed_followups = base_query.filter(FollowupRecord.status == 'completed').count()
    pending_followups = base_query.filter(FollowupRecord.status == 'pending').count()
    failed_followups = base_query.filter(FollowupRecord.status == 'failed').count()
    cancelled_followups = base_query.filter(FollowupRecord.status == 'cancelled').count()

    # 逾期回访数量
    now = datetime.utcnow()
    overdue_followups = base_query.filter(
        FollowupRecord.status == 'pending',
        FollowupRecord.followup_date < now
    ).count()

    # 今日回访数量
    today = datetime.utcnow().date()
    today_followups = base_query.filter(
        FollowupRecord.followup_date >= today,
        FollowupRecord.followup_date < today + timedelta(days=1)
    ).count()

    # 本周回访数量
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=7)
    week_followups = base_query.filter(
        FollowupRecord.followup_date >= week_start,
        FollowupRecord.followup_date < week_end
    ).count()

    # 本月回访数量
    month_start = today.replace(day=1)
    if today.month == 12:
        month_end = today.replace(year=today.year + 1, month=1, day=1)
    else:
        month_end = today.replace(month=today.month + 1, day=1)
    month_followups = base_query.filter(
        FollowupRecord.followup_date >= month_start,
        FollowupRecord.followup_date < month_end
    ).count()

    return jsonify({
        'total_followups': total_followups,
        'completed_followups': completed_followups,
        'pending_followups': pending_followups,
        'failed_followups': failed_followups,
        'cancelled_followups': cancelled_followups,
        'overdue_followups': overdue_followups,
        'today_followups': today_followups,
        'week_followups': week_followups,
        'month_followups': month_followups
    })

@api_bp.route('/followups/export', methods=['GET'])
@jwt_required()
def export_followups():
    """导出回访记录为CSV格式"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 获取查询参数
    status = request.args.get('status')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search', '')

    # 基础查询
    query = FollowupRecord.query.join(Patient).join(FollowupTemplate)

    # 科室权限控制
    if current_user.is_super_admin():
        # 超级管理员可以看到所有回访记录
        pass
    else:
        # 普通用户和科室管理员只能看到自己科室的回访记录
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        query = query.filter(FollowupTemplate.department_id == current_user.department_id)

    # 应用筛选条件
    if status:
        query = query.filter(FollowupRecord.status == status)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FollowupRecord.followup_date >= date_from_obj)
        except ValueError:
            return jsonify({'error': '开始日期格式错误'}), 400

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(FollowupRecord.followup_date < date_to_obj)
        except ValueError:
            return jsonify({'error': '结束日期格式错误'}), 400

    if search:
        # 通过患者数据中的姓名搜索
        query = query.filter(Patient.data.like(f'%{search}%'))

    # 获取所有匹配的记录
    followups = query.order_by(FollowupRecord.followup_date.desc()).all()

    # 收集所有患者模板字段和回访模板字段
    patient_fields = set()
    followup_fields = set()

    for followup in followups:
        # 收集患者模板字段
        if followup.patient and followup.patient.template:
            for field in followup.patient.template.fields:
                patient_fields.add((field.field_name, field.field_label))

        # 收集回访模板字段
        if followup.template:
            for field in followup.template.fields:
                followup_fields.add((field.field_name, field.field_label))

    # 排序字段
    patient_fields = sorted(list(patient_fields), key=lambda x: x[1])
    followup_fields = sorted(list(followup_fields), key=lambda x: x[1])

    # 创建CSV内容
    output = io.StringIO()
    writer = csv.writer(output)

    # 构建表头
    headers = ['回访ID', '患者ID', '回访模板', '回访方式', '计划时间', '实际时间', '状态', '结果', '执行人', '备注', '创建时间']

    # 添加患者录入信息字段
    for field_name, field_label in patient_fields:
        headers.append(f'患者信息-{field_label}')

    # 添加回访内容信息字段
    for field_name, field_label in followup_fields:
        headers.append(f'回访内容-{field_label}')

    writer.writerow(headers)

    # 写入数据
    for followup in followups:
        # 解析患者数据
        patient_data = json.loads(followup.patient.data) if followup.patient.data else {}

        # 获取回访数据
        followup_data = followup.get_data_dict()

        # 状态映射
        status_map = {
            'pending': '待回访',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        }

        # 结果映射
        result_map = {
            'success': '成功',
            'no_answer': '无人接听',
            'refused': '拒绝',
            'invalid_number': '号码无效'
        }

        # 回访方式映射
        method_map = {
            'phone': '电话',
            'sms': '短信',
            'wechat': '微信',
            'email': '邮件',
            'visit': '上门'
        }

        # 基础信息
        row = [
            followup.id,
            followup.patient.id if followup.patient else '',
            followup.template.name if followup.template else '未知',
            method_map.get(followup.followup_method, followup.followup_method),
            followup.followup_date.strftime('%Y-%m-%d %H:%M') if followup.followup_date else '',
            followup.actual_date.strftime('%Y-%m-%d %H:%M') if followup.actual_date else '',
            status_map.get(followup.status, followup.status),
            result_map.get(followup.result, followup.result) if followup.result else '',
            followup.executor.username if followup.executor else '',
            followup.notes or '',
            followup.created_at.strftime('%Y-%m-%d %H:%M') if followup.created_at else ''
        ]

        # 添加患者录入信息
        for field_name, field_label in patient_fields:
            value = patient_data.get(field_name, '')
            # 如果是日期类型，格式化显示
            if isinstance(value, str) and ('date' in field_name.lower() or 'time' in field_name.lower()):
                try:
                    # 尝试解析日期并格式化
                    if 'T' in value or '-' in value:
                        parsed_date = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        value = parsed_date.strftime('%Y-%m-%d')
                except:
                    pass
            row.append(str(value) if value is not None else '')

        # 添加回访内容信息
        for field_name, field_label in followup_fields:
            value = followup_data.get(field_name, '')
            row.append(str(value) if value is not None else '')

        writer.writerow(row)

    # 创建响应
    output.seek(0)
    csv_data = output.getvalue()
    output.close()

    # 生成文件名（使用英文避免编码问题）
    filename = f"followup_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

    # 创建响应，使用字节数据避免编码问题
    csv_bytes = csv_data.encode('utf-8-sig')
    response = make_response(csv_bytes)
    response.headers['Content-Type'] = 'application/octet-stream'
    response.headers['Content-Disposition'] = f'attachment; filename={filename}'

    return response
