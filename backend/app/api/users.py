from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, Department
from . import api_bp

def require_super_admin():
    """检查是否为超级管理员"""
    user_id = int(get_jwt_identity())
    user = User.query.get(user_id)
    if not user or not user.is_super_admin():
        return jsonify({'error': '权限不足'}), 403
    return None

@api_bp.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    """获取用户列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    department_id = request.args.get('department_id', type=int)
    role = request.args.get('role')
    
    query = User.query
    
    # 权限控制
    if current_user.is_super_admin():
        # 超级管理员可以查看所有用户
        pass
    elif current_user.is_dept_admin():
        # 科室管理员只能查看本科室用户
        query = query.filter_by(department_id=current_user.department_id)
    else:
        return jsonify({'error': '权限不足'}), 403
    
    # 筛选条件
    if department_id:
        query = query.filter_by(department_id=department_id)
    if role:
        query = query.filter_by(role=role)
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    users = pagination.items
    
    return jsonify({
        'users': [user.to_dict() for user in users],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/users', methods=['POST'])
@jwt_required()
def create_user():
    """创建用户"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['username', 'email', 'password', 'role']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400
    
    # 权限检查
    if data['role'] == 'super_admin' and not current_user.is_super_admin():
        return jsonify({'error': '只有超级管理员可以创建超级管理员'}), 403
    
    if data['role'] == 'dept_admin' and not current_user.is_super_admin():
        return jsonify({'error': '只有超级管理员可以创建科室管理员'}), 403
    
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400
    
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': '邮箱已存在'}), 400
    
    # 创建用户
    user = User(
        username=data['username'],
        email=data['email'],
        real_name=data.get('real_name'),
        phone=data.get('phone'),
        role=data['role'],
        department_id=data.get('department_id'),
        is_active=data.get('is_active', True)
    )
    user.set_password(data['password'])
    
    # 科室管理员只能在自己科室创建用户
    if current_user.is_dept_admin():
        user.department_id = current_user.department_id
        user.role = 'user'  # 科室管理员只能创建普通用户
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'message': '用户创建成功',
        'user': user.to_dict()
    }), 201

@api_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    """更新用户"""
    current_user_id = int(get_jwt_identity())
    current_user = User.query.get(current_user_id)
    user = User.query.get(user_id)
    
    if not current_user or not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and not (
        current_user.is_dept_admin() and user.department_id == current_user.department_id
    ):
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 更新字段
    if 'real_name' in data:
        user.real_name = data['real_name']
    if 'phone' in data:
        user.phone = data['phone']
    if 'email' in data:
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user.id:
            return jsonify({'error': '邮箱已存在'}), 400
        user.email = data['email']
    
    # 只有超级管理员可以修改角色和科室
    if current_user.is_super_admin():
        if 'role' in data:
            user.role = data['role']
        if 'department_id' in data:
            user.department_id = data['department_id']
        if 'is_active' in data:
            user.is_active = data['is_active']
    
    db.session.commit()
    
    return jsonify({
        'message': '用户更新成功',
        'user': user.to_dict()
    })

@api_bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    """删除用户"""
    current_user_id = int(get_jwt_identity())
    current_user = User.query.get(current_user_id)
    user = User.query.get(user_id)
    
    if not current_user or not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin():
        return jsonify({'error': '只有超级管理员可以删除用户'}), 403
    
    # 不能删除自己
    if user.id == current_user.id:
        return jsonify({'error': '不能删除自己'}), 400
    
    db.session.delete(user)
    db.session.commit()
    
    return jsonify({'message': '用户删除成功'})

@api_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@jwt_required()
def reset_password(user_id):
    """重置用户密码"""
    current_user_id = int(get_jwt_identity())
    current_user = User.query.get(current_user_id)
    user = User.query.get(user_id)
    
    if not current_user or not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and not (
        current_user.is_dept_admin() and user.department_id == current_user.department_id
    ):
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    new_password = data.get('new_password', '123456')  # 默认密码
    
    user.set_password(new_password)
    db.session.commit()
    
    return jsonify({'message': '密码重置成功', 'new_password': new_password})
