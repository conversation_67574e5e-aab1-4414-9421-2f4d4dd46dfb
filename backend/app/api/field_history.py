from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, FieldHistory
from . import api_bp

@api_bp.route('/field-history/suggestions', methods=['GET'])
@jwt_required()
def get_field_suggestions():
    """获取字段输入建议"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 获取参数
    field_name = request.args.get('field_name')
    template_type = request.args.get('template_type')  # patient 或 followup
    query = request.args.get('query', '')
    limit = int(request.args.get('limit', 10))
    
    if not field_name or not template_type:
        return jsonify({'error': '缺少必要参数'}), 400
    
    try:
        suggestions = FieldHistory.get_suggestions(
            field_name=field_name,
            template_type=template_type,
            user_id=user_id,
            query=query,
            limit=limit
        )
        
        return jsonify({
            'suggestions': suggestions
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/field-history/record', methods=['POST'])
@jwt_required()
def record_field_usage():
    """记录字段使用历史"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': '无效的请求数据'}), 400
    
    field_name = data.get('field_name')
    field_value = data.get('field_value')
    template_type = data.get('template_type')
    template_id = data.get('template_id')
    
    if not all([field_name, field_value, template_type]):
        return jsonify({'error': '缺少必要参数'}), 400
    
    try:
        history_record = FieldHistory.record_usage(
            field_name=field_name,
            field_value=field_value,
            template_type=template_type,
            template_id=template_id,
            user_id=user_id
        )
        
        if history_record:
            db.session.commit()
            return jsonify({
                'message': '记录成功',
                'record': history_record.to_dict()
            })
        else:
            return jsonify({'message': '无需记录'})
            
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@api_bp.route('/field-history/batch-record', methods=['POST'])
@jwt_required()
def batch_record_field_usage():
    """批量记录字段使用历史"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': '无效的请求数据'}), 400
    
    fields = data.get('fields', [])
    template_type = data.get('template_type')
    template_id = data.get('template_id')
    
    if not template_type:
        return jsonify({'error': '缺少模板类型'}), 400
    
    try:
        recorded_count = 0
        for field_data in fields:
            field_name = field_data.get('field_name')
            field_value = field_data.get('field_value')
            
            if field_name and field_value:
                history_record = FieldHistory.record_usage(
                    field_name=field_name,
                    field_value=field_value,
                    template_type=template_type,
                    template_id=template_id,
                    user_id=user_id
                )
                if history_record:
                    recorded_count += 1
        
        db.session.commit()
        return jsonify({
            'message': f'成功记录 {recorded_count} 个字段',
            'recorded_count': recorded_count
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@api_bp.route('/field-history/cleanup', methods=['POST'])
@jwt_required()
def cleanup_field_history():
    """清理旧的字段历史记录（管理员功能）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user or not current_user.is_super_admin():
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json() or {}
    days = data.get('days', 90)
    max_records_per_field = data.get('max_records_per_field', 50)
    
    try:
        FieldHistory.cleanup_old_records(days, max_records_per_field)
        db.session.commit()
        
        return jsonify({
            'message': '清理完成',
            'days': days,
            'max_records_per_field': max_records_per_field
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@api_bp.route('/field-history/stats', methods=['GET'])
@jwt_required()
def get_field_history_stats():
    """获取字段历史统计信息"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    try:
        # 用户的历史记录统计
        total_records = FieldHistory.query.filter_by(user_id=user_id).count()
        
        # 按模板类型统计
        patient_records = FieldHistory.query.filter_by(
            user_id=user_id, 
            template_type='patient'
        ).count()
        
        followup_records = FieldHistory.query.filter_by(
            user_id=user_id, 
            template_type='followup'
        ).count()
        
        # 最常用的字段值
        from sqlalchemy import func
        top_values = db.session.query(
            FieldHistory.field_name,
            FieldHistory.field_value,
            FieldHistory.usage_count
        ).filter_by(user_id=user_id).order_by(
            FieldHistory.usage_count.desc()
        ).limit(10).all()
        
        return jsonify({
            'total_records': total_records,
            'patient_records': patient_records,
            'followup_records': followup_records,
            'top_values': [
                {
                    'field_name': item[0],
                    'field_value': item[1],
                    'usage_count': item[2]
                }
                for item in top_values
            ]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
