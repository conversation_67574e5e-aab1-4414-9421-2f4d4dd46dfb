from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User, PatientTemplate, PatientTemplateField
from app.services.template_generator import template_generator
from . import api_bp

@api_bp.route('/patient-templates', methods=['GET'])
@jwt_required()
def get_patient_templates():
    """获取患者信息模板列表"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    department_id = request.args.get('department_id', type=int)
    
    query = PatientTemplate.query

    print(f"当前用户: {current_user.username}, 科室ID: {current_user.department_id}, 是否超级管理员: {current_user.is_super_admin()}")

    # 权限控制
    if current_user.is_super_admin():
        # 超级管理员可以查看所有模板
        print("超级管理员，可以查看所有模板")
        pass
    else:
        # 其他用户只能查看本科室的模板
        print(f"普通用户，只能查看科室 {current_user.department_id} 的模板")
        query = query.filter(PatientTemplate.department_id == current_user.department_id)

    # 科室筛选
    if department_id:
        query = query.filter(PatientTemplate.department_id == department_id)

    # 只显示激活的模板
    query = query.filter(PatientTemplate.is_active == True)

    # 调试：查看查询结果
    all_templates = query.all()
    print(f"查询到的模板数量: {len(all_templates)}")
    for template in all_templates:
        print(f"模板: {template.name}, 科室ID: {template.department_id}, 激活状态: {template.is_active}")
    
    # 分页
    pagination = query.order_by(PatientTemplate.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    templates = [template.to_dict() for template in pagination.items]
    
    return jsonify({
        'templates': templates,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/patient-templates/<int:template_id>', methods=['GET'])
@jwt_required()
def get_patient_template(template_id):
    """获取患者信息模板详情"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = PatientTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    return jsonify({'template': template.to_dict()})

@api_bp.route('/patient-templates', methods=['POST'])
@jwt_required()
def create_patient_template():
    """创建患者信息模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and not current_user.is_dept_admin():
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field}不能为空'}), 400
    
    # 创建模板
    template = PatientTemplate(
        name=data['name'],
        description=data.get('description'),
        department_id=data.get('department_id', current_user.department_id),
        is_active=data.get('is_active', True),
        is_default=data.get('is_default', False),
        created_by=current_user.id
    )
    
    # 非超级管理员只能在自己科室创建模板
    if not current_user.is_super_admin():
        template.department_id = current_user.department_id
    
    db.session.add(template)
    db.session.flush()  # 获取模板ID
    
    # 创建模板字段
    fields_data = data.get('fields', [])

    # 如果没有提供字段配置，则根据模板名称自动生成
    if not fields_data:
        fields_data = template_generator.generate_patient_template_fields(template.name)

    # 验证字段名是否重复
    field_names = [field['field_name'] for field in fields_data]
    if len(field_names) != len(set(field_names)):
        return jsonify({'error': '字段名不能重复'}), 400

    for field_data in fields_data:
        field = PatientTemplateField(
            template_id=template.id,
            field_name=field_data['field_name'],
            field_label=field_data['field_label'],
            field_type=field_data['field_type'],
            order=field_data.get('order', 0),
            is_required=field_data.get('is_required', False)
        )

        if field_data.get('field_config'):
            field.set_config(field_data['field_config'])

        db.session.add(field)
    
    db.session.commit()
    
    return jsonify({
        'message': '患者信息模板创建成功',
        'template': template.to_dict()
    }), 201

@api_bp.route('/patient-templates/<int:template_id>', methods=['PUT'])
@jwt_required()
def update_patient_template(template_id):
    """更新患者信息模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = PatientTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    
    # 更新基本信息
    if 'name' in data:
        template.name = data['name']
    if 'description' in data:
        template.description = data['description']
    if 'is_active' in data:
        template.is_active = data['is_active']
    if 'is_default' in data:
        template.is_default = data['is_default']
    
    # 更新字段
    if 'fields' in data:
        # 验证字段名是否重复
        field_names = [field['field_name'] for field in data['fields']]
        if len(field_names) != len(set(field_names)):
            return jsonify({'error': '字段名不能重复'}), 400

        # 删除现有字段
        template.fields.delete()

        # 添加新字段
        for field_data in data['fields']:
            field = PatientTemplateField(
                template_id=template.id,
                field_name=field_data['field_name'],
                field_label=field_data['field_label'],
                field_type=field_data['field_type'],
                order=field_data.get('order', 0),
                is_required=field_data.get('is_required', False)
            )
            
            if field_data.get('field_config'):
                field.set_config(field_data['field_config'])
            
            db.session.add(field)
    
    db.session.commit()
    
    return jsonify({
        'message': '患者信息模板更新成功',
        'template': template.to_dict()
    })

@api_bp.route('/patient-templates/preview-fields', methods=['POST'])
@jwt_required()
def preview_patient_template_fields():
    """预览根据模板名称自动生成的字段"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)

    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    data = request.get_json()
    template_name = data.get('name')

    if not template_name:
        return jsonify({'error': '模板名称不能为空'}), 400

    # 生成预览字段
    fields = template_generator.generate_patient_template_fields(template_name)

    return jsonify({
        'fields': fields,
        'total_fields': len(fields)
    })

@api_bp.route('/patient-templates/<int:template_id>', methods=['DELETE'])
@jwt_required()
def delete_patient_template(template_id):
    """删除患者信息模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    template = PatientTemplate.query.get(template_id)
    
    if not current_user or not template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    # 检查是否有患者在使用此模板
    if len(template.patients) > 0:
        return jsonify({'error': '该模板正在被使用，无法删除'}), 400
    
    db.session.delete(template)
    db.session.commit()
    
    return jsonify({'message': '患者信息模板删除成功'})

@api_bp.route('/patient-templates/<int:template_id>/copy', methods=['POST'])
@jwt_required()
def copy_patient_template(template_id):
    """复制患者信息模板"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    original_template = PatientTemplate.query.get(template_id)
    
    if not current_user or not original_template:
        return jsonify({'error': '模板不存在'}), 404
    
    # 权限检查
    if not current_user.is_super_admin() and original_template.department_id != current_user.department_id:
        return jsonify({'error': '权限不足'}), 403
    
    data = request.get_json()
    new_name = data.get('name', f"{original_template.name}_副本")
    
    # 创建新模板
    new_template = PatientTemplate(
        name=new_name,
        description=original_template.description,
        department_id=original_template.department_id,
        is_active=True,
        is_default=False,
        created_by=current_user.id
    )
    
    db.session.add(new_template)
    db.session.flush()
    
    # 复制字段
    for field in original_template.fields:
        new_field = PatientTemplateField(
            template_id=new_template.id,
            field_name=field.field_name,
            field_label=field.field_label,
            field_type=field.field_type,
            field_config=field.field_config,
            order=field.order,
            is_required=field.is_required
        )
        db.session.add(new_field)
    
    db.session.commit()
    
    return jsonify({
        'message': '患者信息模板复制成功',
        'template': new_template.to_dict()
    }), 201
