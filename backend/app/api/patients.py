from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
from app import db
from app.models import User, Patient, PatientTemplate
from . import api_bp
import json

@api_bp.route('/patients', methods=['GET'])
@jwt_required()
def get_patients():
    """获取患者列表（只返回模板和动态字段数据）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)

    query = Patient.query

    # 科室权限控制
    if current_user.is_super_admin():
        # 超级管理员可以看到所有患者
        pass
    else:
        # 普通用户和科室管理员只能看到自己科室的患者
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        # 通过患者模版的科室ID来过滤患者
        query = query.join(PatientTemplate).filter(PatientTemplate.department_id == current_user.department_id)

    # 如果有搜索条件，添加搜索过滤
    if search:
        # 搜索患者的JSON数据中的姓名、电话等字段
        # 使用SQL的JSON函数进行搜索
        search_pattern = f'%{search}%'
        query = query.filter(
            db.or_(
                Patient.data.like(search_pattern),  # 简单的文本搜索
                # 可以根据需要添加更精确的JSON字段搜索
            )
        )

    pagination = query.order_by(Patient.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    patients = pagination.items
    return jsonify({
        'patients': [p.to_dict() for p in patients],
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page
    })

@api_bp.route('/patients', methods=['POST'])
@jwt_required()
def create_patient():
    """创建患者（只需模板ID和动态字段数据）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404
    data = request.get_json()
    # 必须有模板ID
    if not data.get('template_id'):
        return jsonify({'error': 'template_id不能为空'}), 400

    # 验证模版是否存在且用户有权限使用
    template = PatientTemplate.query.get(data['template_id'])
    if not template:
        return jsonify({'error': '患者信息模版不存在'}), 404

    # 权限检查：非超级管理员只能使用自己科室的模版
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if template.department_id != current_user.department_id:
            return jsonify({'error': '无权使用该科室的患者信息模版'}), 403

    # 创建患者
    patient = Patient(
        template_id=data.get('template_id'),
        data=json.dumps(data.get('data', {}))
    )
    db.session.add(patient)
    db.session.commit()
    return jsonify({
        'message': '患者创建成功',
        'patient': {
            'id': patient.id,
            'template_id': patient.template_id,
            'template': patient.template.to_dict() if patient.template else None,
            'data': patient.get_data_dict(),
            'created_at': patient.created_at.isoformat() if patient.created_at else None
        }
    }), 201

@api_bp.route('/patients/<int:patient_id>', methods=['GET'])
@jwt_required()
def get_patient(patient_id):
    """获取患者详情（只返回模板和动态字段数据）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    patient = Patient.query.get(patient_id)
    if not current_user or not patient:
        return jsonify({'error': '患者不存在'}), 404
    return jsonify({'patient': patient.to_dict()})

@api_bp.route('/patients/<int:patient_id>', methods=['PUT'])
@jwt_required()
def update_patient(patient_id):
    """更新患者（只允许更新动态字段数据）"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    patient = Patient.query.get(patient_id)
    if not current_user or not patient:
        return jsonify({'error': '患者不存在'}), 404

    # 权限检查：非超级管理员只能更新自己科室的患者
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if patient.template.department_id != current_user.department_id:
            return jsonify({'error': '无权更新该科室的患者信息'}), 403

    data = request.get_json()
    if 'data' in data:
        patient.data = json.dumps(data['data'])
    db.session.commit()
    return jsonify({'message': '患者更新成功'})

@api_bp.route('/patients/<int:patient_id>', methods=['DELETE'])
@jwt_required()
def delete_patient(patient_id):
    """删除患者"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    patient = Patient.query.get(patient_id)
    if not current_user or not patient:
        return jsonify({'error': '患者不存在'}), 404

    # 权限检查：非超级管理员只能删除自己科室的患者
    if not current_user.is_super_admin():
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        if patient.template.department_id != current_user.department_id:
            return jsonify({'error': '无权删除该科室的患者'}), 403

    # 检查是否有关联的随访记录
    from app.models import FollowupRecord
    followup_count = FollowupRecord.query.filter_by(patient_id=patient_id).count()
    if followup_count > 0:
        return jsonify({
            'error': f'无法删除患者，存在{followup_count}条关联的随访记录。请先删除相关随访记录。'
        }), 400

    db.session.delete(patient)
    db.session.commit()
    return jsonify({'message': '患者删除成功'})

@api_bp.route('/patients/stats', methods=['GET'])
@jwt_required()
def get_patient_stats():
    """获取患者统计信息"""
    user_id = int(get_jwt_identity())
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': '用户不存在'}), 404

    # 基础查询
    base_query = Patient.query

    # 科室权限控制
    if current_user.is_super_admin():
        # 超级管理员可以看到所有患者
        pass
    else:
        # 普通用户和科室管理员只能看到自己科室的患者
        if not current_user.department_id:
            return jsonify({'error': '用户未分配科室'}), 403
        # 通过患者模版的科室ID来过滤患者
        base_query = base_query.join(PatientTemplate).filter(PatientTemplate.department_id == current_user.department_id)

    # 统计患者总数
    total_patients = base_query.count()

    # 统计今日新增患者
    today = datetime.utcnow().date()
    today_patients = base_query.filter(
        Patient.created_at >= today,
        Patient.created_at < today + timedelta(days=1)
    ).count()

    # 统计本周新增患者
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=7)
    week_patients = base_query.filter(
        Patient.created_at >= week_start,
        Patient.created_at < week_end
    ).count()

    # 统计本月新增患者
    month_start = today.replace(day=1)
    if today.month == 12:
        month_end = today.replace(year=today.year + 1, month=1, day=1)
    else:
        month_end = today.replace(month=today.month + 1, day=1)
    month_patients = base_query.filter(
        Patient.created_at >= month_start,
        Patient.created_at < month_end
    ).count()

    return jsonify({
        'total_patients': total_patients,
        'today_patients': today_patients,
        'week_patients': week_patients,
        'month_patients': month_patients
    })
