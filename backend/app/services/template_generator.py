"""
模板字段自动生成服务
根据模板名称自动生成合适的字段配置
"""

import re
import json
from typing import List, Dict, Any


class TemplateFieldGenerator:
    """模板字段自动生成器"""
    
    def __init__(self):
        # 患者信息模板的预设字段模式
        self.patient_field_patterns = {
            # 基础信息字段
            'basic': [
                {'field_name': 'name', 'field_label': '姓名', 'field_type': 'text', 'is_required': True, 'order': 1},
                {'field_name': 'gender', 'field_label': '性别', 'field_type': 'select', 'is_required': True, 'order': 2,
                 'field_config': json.dumps({'options': ['男', '女']})},
                {'field_name': 'age', 'field_label': '年龄', 'field_type': 'number', 'is_required': True, 'order': 3},
                {'field_name': 'phone', 'field_label': '联系电话', 'field_type': 'text', 'is_required': True, 'order': 4},
                {'field_name': 'address', 'field_label': '家庭住址', 'field_type': 'textarea', 'is_required': False, 'order': 5},
            ],
            
            # 产科相关字段
            'obstetrics': [
                {'field_name': 'gestational_age', 'field_label': '孕周', 'field_type': 'text', 'is_required': True, 'order': 6},
                {'field_name': 'delivery_method', 'field_label': '分娩方式', 'field_type': 'select', 'is_required': True, 'order': 7,
                 'field_config': json.dumps({'options': ['顺产', '剖宫产', '助产']})},
                {'field_name': 'birth_weight', 'field_label': '新生儿体重(g)', 'field_type': 'number', 'is_required': True, 'order': 8},
                {'field_name': 'birth_length', 'field_label': '新生儿身长(cm)', 'field_type': 'number', 'is_required': False, 'order': 9},
                {'field_name': 'apgar_score', 'field_label': 'Apgar评分', 'field_type': 'text', 'is_required': False, 'order': 10},
                {'field_name': 'complications', 'field_label': '并发症', 'field_type': 'textarea', 'is_required': False, 'order': 11},
            ],
            
            # 儿科相关字段
            'pediatrics': [
                {'field_name': 'birth_date', 'field_label': '出生日期', 'field_type': 'date', 'is_required': True, 'order': 6},
                {'field_name': 'birth_weight', 'field_label': '出生体重(g)', 'field_type': 'number', 'is_required': True, 'order': 7},
                {'field_name': 'current_weight', 'field_label': '当前体重(kg)', 'field_type': 'number', 'is_required': False, 'order': 8},
                {'field_name': 'current_height', 'field_label': '当前身高(cm)', 'field_type': 'number', 'is_required': False, 'order': 9},
                {'field_name': 'feeding_method', 'field_label': '喂养方式', 'field_type': 'select', 'is_required': False, 'order': 10,
                 'field_config': json.dumps({'options': ['母乳喂养', '人工喂养', '混合喂养']})},
                {'field_name': 'vaccination_status', 'field_label': '疫苗接种情况', 'field_type': 'textarea', 'is_required': False, 'order': 11},
            ],
            
            # 外科相关字段
            'surgery': [
                {'field_name': 'surgery_date', 'field_label': '手术日期', 'field_type': 'date', 'is_required': True, 'order': 6},
                {'field_name': 'surgery_type', 'field_label': '手术类型', 'field_type': 'text', 'is_required': True, 'order': 7},
                {'field_name': 'anesthesia_method', 'field_label': '麻醉方式', 'field_type': 'select', 'is_required': False, 'order': 8,
                 'field_config': json.dumps({'options': ['全身麻醉', '局部麻醉', '椎管内麻醉']})},
                {'field_name': 'surgery_duration', 'field_label': '手术时长(分钟)', 'field_type': 'number', 'is_required': False, 'order': 9},
                {'field_name': 'postop_complications', 'field_label': '术后并发症', 'field_type': 'textarea', 'is_required': False, 'order': 10},
            ],
            
            # 内科相关字段
            'internal': [
                {'field_name': 'admission_date', 'field_label': '入院日期', 'field_type': 'date', 'is_required': True, 'order': 6},
                {'field_name': 'discharge_date', 'field_label': '出院日期', 'field_type': 'date', 'is_required': True, 'order': 7},
                {'field_name': 'primary_diagnosis', 'field_label': '主要诊断', 'field_type': 'text', 'is_required': True, 'order': 8},
                {'field_name': 'secondary_diagnosis', 'field_label': '次要诊断', 'field_type': 'textarea', 'is_required': False, 'order': 9},
                {'field_name': 'medications', 'field_label': '出院用药', 'field_type': 'textarea', 'is_required': False, 'order': 10},
                {'field_name': 'follow_up_plan', 'field_label': '随访计划', 'field_type': 'textarea', 'is_required': False, 'order': 11},
            ],
            
            # 急诊相关字段
            'emergency': [
                {'field_name': 'visit_date', 'field_label': '就诊日期', 'field_type': 'date', 'is_required': True, 'order': 6},
                {'field_name': 'chief_complaint', 'field_label': '主诉', 'field_type': 'textarea', 'is_required': True, 'order': 7},
                {'field_name': 'triage_level', 'field_label': '分诊级别', 'field_type': 'select', 'is_required': True, 'order': 8,
                 'field_config': json.dumps({'options': ['I级(危重)', 'II级(急症)', 'III级(急症)', 'IV级(非急症)', 'V级(非急症)']})},
                {'field_name': 'treatment_result', 'field_label': '处理结果', 'field_type': 'select', 'is_required': True, 'order': 9,
                 'field_config': json.dumps({'options': ['治愈', '好转', '转院', '死亡', '其他']})},
            ],
        }
        
        # 回访模板的预设字段模式
        self.followup_field_patterns = {
            # 基础回访字段
            'basic': [
                {'field_name': 'contact_status', 'field_label': '联系状态', 'field_type': 'select', 'is_required': True, 'order': 1,
                 'field_config': json.dumps({'options': ['已联系', '未接通', '拒绝回访', '号码错误']})},
                {'field_name': 'overall_condition', 'field_label': '总体情况', 'field_type': 'select', 'is_required': False, 'order': 2,
                 'field_config': json.dumps({'options': ['很好', '良好', '一般', '较差', '很差']})},
                {'field_name': 'satisfaction', 'field_label': '满意度', 'field_type': 'select', 'is_required': False, 'order': 3,
                 'field_config': json.dumps({'options': ['非常满意', '满意', '一般', '不满意', '非常不满意']})},
                {'field_name': 'notes', 'field_label': '备注', 'field_type': 'textarea', 'is_required': False, 'order': 4},
            ],
            
            # 产科回访字段
            'obstetrics': [
                {'field_name': 'breastfeeding_status', 'field_label': '母乳喂养情况', 'field_type': 'select', 'is_required': False, 'order': 5,
                 'field_config': json.dumps({'options': ['纯母乳', '混合喂养', '人工喂养', '已断奶']})},
                {'field_name': 'wound_healing', 'field_label': '伤口愈合情况', 'field_type': 'select', 'is_required': False, 'order': 6,
                 'field_config': json.dumps({'options': ['愈合良好', '轻度红肿', '有渗液', '感染', '无伤口']})},
                {'field_name': 'lochia_status', 'field_label': '恶露情况', 'field_type': 'select', 'is_required': False, 'order': 7,
                 'field_config': json.dumps({'options': ['正常', '量多', '有异味', '颜色异常', '已干净']})},
                {'field_name': 'baby_condition', 'field_label': '婴儿情况', 'field_type': 'textarea', 'is_required': False, 'order': 8},
            ],
            
            # 手术回访字段
            'surgery': [
                {'field_name': 'pain_level', 'field_label': '疼痛程度', 'field_type': 'select', 'is_required': False, 'order': 5,
                 'field_config': json.dumps({'options': ['无痛', '轻微', '中等', '严重', '剧烈']})},
                {'field_name': 'wound_status', 'field_label': '切口情况', 'field_type': 'select', 'is_required': False, 'order': 6,
                 'field_config': json.dumps({'options': ['愈合良好', '轻度红肿', '有渗液', '裂开', '感染']})},
                {'field_name': 'mobility', 'field_label': '活动能力', 'field_type': 'select', 'is_required': False, 'order': 7,
                 'field_config': json.dumps({'options': ['正常', '轻度受限', '中度受限', '重度受限', '卧床']})},
                {'field_name': 'medication_compliance', 'field_label': '术后用药依从性', 'field_type': 'select', 'is_required': False, 'order': 8,
                 'field_config': json.dumps({'options': ['完全遵医嘱', '基本遵医嘱', '偶尔忘记', '经常忘记', '自行停药']})},
            ],

            # 慢病回访字段
            'chronic': [
                {'field_name': 'symptom_control', 'field_label': '症状控制情况', 'field_type': 'select', 'is_required': False, 'order': 5,
                 'field_config': json.dumps({'options': ['控制良好', '基本控制', '控制一般', '控制不佳', '症状加重']})},
                {'field_name': 'medication_adherence', 'field_label': '慢病用药依从性', 'field_type': 'select', 'is_required': False, 'order': 6,
                 'field_config': json.dumps({'options': ['完全依从', '基本依从', '偶尔漏服', '经常漏服', '自行调药']})},
                {'field_name': 'lifestyle_changes', 'field_label': '生活方式改变', 'field_type': 'textarea', 'is_required': False, 'order': 7},
                {'field_name': 'next_visit_plan', 'field_label': '下次就诊计划', 'field_type': 'text', 'is_required': False, 'order': 8},
            ],
        }
    
    def generate_patient_template_fields(self, template_name: str) -> List[Dict[str, Any]]:
        """根据患者信息模板名称生成字段配置"""
        fields = []
        
        # 所有模板都包含基础字段
        fields.extend(self.patient_field_patterns['basic'])
        
        # 根据模板名称关键词添加专科字段
        name_lower = template_name.lower()
        
        if any(keyword in name_lower for keyword in ['产科', '妇产', '分娩', '孕产', '产房', '妇幼']):
            fields.extend(self.patient_field_patterns['obstetrics'])
        elif any(keyword in name_lower for keyword in ['儿科', '新生儿', '小儿', '婴儿', '儿童']):
            fields.extend(self.patient_field_patterns['pediatrics'])
        elif any(keyword in name_lower for keyword in ['外科', '手术', '骨科', '泌尿', '胸外', '神外', '普外']):
            fields.extend(self.patient_field_patterns['surgery'])
        elif any(keyword in name_lower for keyword in ['内科', '心内', '呼吸', '消化', '内分泌', '肾内', '神内']):
            fields.extend(self.patient_field_patterns['internal'])
        elif any(keyword in name_lower for keyword in ['急诊', '急救', '抢救', 'icu', '重症']):
            fields.extend(self.patient_field_patterns['emergency'])
        
        return fields
    
    def generate_followup_template_fields(self, template_name: str) -> List[Dict[str, Any]]:
        """根据回访模板名称生成字段配置"""
        fields = []
        
        # 所有模板都包含基础字段
        fields.extend(self.followup_field_patterns['basic'])
        
        # 根据模板名称关键词添加专科字段
        name_lower = template_name.lower()
        
        if any(keyword in name_lower for keyword in ['产科', '妇产', '分娩', '孕产', '产后', '母婴']):
            fields.extend(self.followup_field_patterns['obstetrics'])
        elif any(keyword in name_lower for keyword in ['手术', '术后', '外科', '骨科', '泌尿', '胸外', '神外', '普外']):
            fields.extend(self.followup_field_patterns['surgery'])
        elif any(keyword in name_lower for keyword in ['慢病', '糖尿病', '高血压', '慢性', '随访', '复查']):
            fields.extend(self.followup_field_patterns['chronic'])
        
        return fields
    
    def get_suggested_followup_schedule(self, template_name: str) -> Dict[str, Any]:
        """根据模板名称建议回访时间安排"""
        name_lower = template_name.lower()
        
        if any(keyword in name_lower for keyword in ['产科', '妇产', '产后']):
            return {"days": [3, 7, 30, 90], "time": "10:00"}
        elif any(keyword in name_lower for keyword in ['手术', '术后']):
            return {"days": [1, 3, 7, 30], "time": "09:00"}
        elif any(keyword in name_lower for keyword in ['慢病', '糖尿病', '高血压']):
            return {"days": [30, 90, 180], "time": "14:00"}
        elif any(keyword in name_lower for keyword in ['急诊', '急救']):
            return {"days": [1, 3], "time": "16:00"}
        else:
            return {"days": [1, 3, 7], "time": "09:00"}
    
    def get_suggested_followup_method(self, template_name: str) -> str:
        """根据模板名称建议回访方式"""
        name_lower = template_name.lower()
        
        if any(keyword in name_lower for keyword in ['急诊', '急救', '重症']):
            return 'phone'
        elif any(keyword in name_lower for keyword in ['慢病', '随访', '复查']):
            return 'phone'
        elif any(keyword in name_lower for keyword in ['产科', '妇产', '产后']):
            return 'phone'
        else:
            return 'phone'


# 创建全局实例
template_generator = TemplateFieldGenerator()
