"""
字段名称自动生成服务
根据中文字段标签自动生成规范的英文字段名称
"""

import re
from typing import List, Dict, Any


class FieldNameGenerator:
    """字段名称自动生成器"""
    
    def __init__(self):
        # 常用医疗术语中英文对照表
        self.medical_terms = {
            # 基础信息
            '姓名': 'name',
            '性别': 'gender', 
            '年龄': 'age',
            '出生日期': 'birth_date',
            '身份证号': 'id_number',
            '联系电话': 'phone',
            '手机号': 'mobile',
            '电话': 'phone',
            '地址': 'address',
            '住址': 'address',
            '家庭住址': 'home_address',
            '邮编': 'postal_code',
            '邮箱': 'email',
            '职业': 'occupation',
            '婚姻状况': 'marital_status',
            '紧急联系人': 'emergency_contact',
            '紧急联系电话': 'emergency_phone',
            
            # 医疗信息
            '病历号': 'medical_record_number',
            '住院号': 'admission_number',
            '床号': 'bed_number',
            '科室': 'department',
            '医生': 'doctor',
            '主治医生': 'attending_doctor',
            '入院日期': 'admission_date',
            '出院日期': 'discharge_date',
            '住院天数': 'hospital_days',
            '诊断': 'diagnosis',
            '主要诊断': 'primary_diagnosis',
            '次要诊断': 'secondary_diagnosis',
            '病史': 'medical_history',
            '过敏史': 'allergy_history',
            '家族史': 'family_history',
            '现病史': 'present_illness',
            '既往史': 'past_history',
            
            # 产科相关
            '孕周': 'gestational_age',
            '胎次': 'gravida',
            '产次': 'para',
            '分娩方式': 'delivery_method',
            '新生儿体重': 'birth_weight',
            '新生儿身长': 'birth_length',
            '新生儿性别': 'baby_gender',
            'Apgar评分': 'apgar_score',
            '胎盘重量': 'placenta_weight',
            '羊水': 'amniotic_fluid',
            '脐带': 'umbilical_cord',
            '会阴': 'perineum',
            '产后出血': 'postpartum_hemorrhage',
            '母乳喂养': 'breastfeeding',
            '恶露': 'lochia',
            '子宫复旧': 'uterine_involution',
            
            # 儿科相关
            '出生体重': 'birth_weight',
            '当前体重': 'current_weight',
            '当前身高': 'current_height',
            '头围': 'head_circumference',
            '胸围': 'chest_circumference',
            '喂养方式': 'feeding_method',
            '疫苗接种': 'vaccination',
            '疫苗接种情况': 'vaccination_status',
            '发育情况': 'development_status',
            '营养状况': 'nutritional_status',
            
            # 外科相关
            '手术日期': 'surgery_date',
            '手术类型': 'surgery_type',
            '手术名称': 'surgery_name',
            '麻醉方式': 'anesthesia_method',
            '手术时长': 'surgery_duration',
            '术前诊断': 'preoperative_diagnosis',
            '术后诊断': 'postoperative_diagnosis',
            '手术医生': 'surgeon',
            '麻醉医生': 'anesthesiologist',
            '切口': 'incision',
            '切口愈合': 'wound_healing',
            '术后并发症': 'postoperative_complications',
            '引流': 'drainage',
            
            # 内科相关
            '主诉': 'chief_complaint',
            '现病史': 'present_illness',
            '体格检查': 'physical_examination',
            '辅助检查': 'auxiliary_examination',
            '实验室检查': 'laboratory_test',
            '影像学检查': 'imaging_examination',
            '心电图': 'ecg',
            '血压': 'blood_pressure',
            '心率': 'heart_rate',
            '体温': 'temperature',
            '呼吸': 'respiration',
            '血糖': 'blood_glucose',
            '血脂': 'blood_lipid',
            
            # 用药相关
            '用药': 'medication',
            '药物': 'medicine',
            '出院用药': 'discharge_medication',
            '用药史': 'medication_history',
            '用药依从性': 'medication_compliance',
            '药物过敏': 'drug_allergy',
            '剂量': 'dosage',
            '用法': 'usage',
            '频次': 'frequency',
            
            # 回访相关
            '联系状态': 'contact_status',
            '回访时间': 'followup_time',
            '回访方式': 'followup_method',
            '回访结果': 'followup_result',
            '总体情况': 'overall_condition',
            '满意度': 'satisfaction',
            '建议': 'suggestion',
            '备注': 'notes',
            '下次回访': 'next_followup',
            '症状改善': 'symptom_improvement',
            '康复情况': 'recovery_status',
            
            # 急诊相关
            '就诊日期': 'visit_date',
            '分诊级别': 'triage_level',
            '处理结果': 'treatment_result',
            '转归': 'outcome',
            '急诊科': 'emergency_department',
            '抢救': 'resuscitation',
            '观察': 'observation',
            
            # 通用字段
            '状态': 'status',
            '类型': 'type',
            '级别': 'level',
            '程度': 'degree',
            '情况': 'condition',
            '结果': 'result',
            '时间': 'time',
            '日期': 'date',
            '编号': 'number',
            '代码': 'code',
            '描述': 'description',
            '说明': 'description',
            '详情': 'details',
            '其他': 'other',
            '是否': 'is',
            '有无': 'has',
        }
        
        # 数字和单位的处理
        self.unit_mappings = {
            '(g)': '_g',
            '(kg)': '_kg', 
            '(cm)': '_cm',
            '(mm)': '_mm',
            '(ml)': '_ml',
            '(分钟)': '_minutes',
            '(小时)': '_hours',
            '(天)': '_days',
            '(周)': '_weeks',
            '(月)': '_months',
            '(年)': '_years',
            '(次)': '_times',
            '(度)': '_degree',
            '(%)': '_percent',
        }
    
    def generate_field_name(self, field_label: str, existing_names: List[str] = None) -> str:
        """
        根据字段标签生成字段名称
        
        Args:
            field_label: 字段标签（中文）
            existing_names: 已存在的字段名称列表，用于去重
            
        Returns:
            生成的字段名称（英文）
        """
        if not field_label:
            return 'field'
        
        if existing_names is None:
            existing_names = []
        
        # 清理输入
        label = field_label.strip()
        
        # 先尝试直接匹配
        if label in self.medical_terms:
            base_name = self.medical_terms[label]
        else:
            # 处理包含单位的字段
            base_name = self._process_with_units(label)
            
            # 如果还是没有匹配，尝试部分匹配
            if not base_name or base_name == label:
                base_name = self._partial_match(label)
            
            # 最后尝试拼音转换
            if not base_name or base_name == label:
                base_name = self._chinese_to_pinyin(label)
        
        # 确保字段名称符合规范
        base_name = self._normalize_field_name(base_name)
        
        # 处理重复名称
        final_name = self._handle_duplicates(base_name, existing_names)
        
        return final_name
    
    def _process_with_units(self, label: str) -> str:
        """处理包含单位的字段标签"""
        for unit, suffix in self.unit_mappings.items():
            if unit in label:
                base_label = label.replace(unit, '').strip()
                if base_label in self.medical_terms:
                    return self.medical_terms[base_label] + suffix
        return label
    
    def _partial_match(self, label: str) -> str:
        """部分匹配医疗术语"""
        # 尝试找到包含关键词的匹配
        for term, english in self.medical_terms.items():
            if term in label or label in term:
                # 如果是完全包含，使用原英文名
                if term in label:
                    # 处理复合词，如"术后并发症"包含"并发症"
                    prefix = label.replace(term, '').strip()
                    if prefix:
                        prefix_english = self._chinese_to_pinyin(prefix)
                        return f"{prefix_english}_{english}"
                    return english
        
        return label
    
    def _chinese_to_pinyin(self, text: str) -> str:
        """
        简单的中文转拼音逻辑
        这里使用基础的字符映射，实际项目中可以使用pypinyin库
        """
        # 基础的中文字符映射
        char_map = {
            '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu',
            '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi',
            '血': 'blood', '尿': 'urine', '便': 'stool', '痰': 'sputum',
            '汗': 'sweat', '泪': 'tear', '唾': 'saliva',
            '头': 'head', '颈': 'neck', '胸': 'chest', '腹': 'abdomen',
            '背': 'back', '腰': 'waist', '臀': 'hip', '腿': 'leg',
            '脚': 'foot', '手': 'hand', '臂': 'arm', '肩': 'shoulder',
            '心': 'heart', '肺': 'lung', '肝': 'liver', '肾': 'kidney',
            '胃': 'stomach', '肠': 'intestine', '脑': 'brain', '眼': 'eye',
            '耳': 'ear', '鼻': 'nose', '口': 'mouth', '舌': 'tongue',
            '牙': 'tooth', '喉': 'throat', '颈': 'neck',
            '左': 'left', '右': 'right', '上': 'upper', '下': 'lower',
            '前': 'anterior', '后': 'posterior', '内': 'inner', '外': 'outer',
            '大': 'large', '小': 'small', '长': 'long', '短': 'short',
            '高': 'high', '低': 'low', '深': 'deep', '浅': 'shallow',
            '重': 'heavy', '轻': 'light', '粗': 'thick', '细': 'thin',
            '急': 'acute', '慢': 'chronic', '新': 'new', '旧': 'old',
            '好': 'good', '坏': 'bad', '正': 'normal', '异': 'abnormal',
            '有': 'has', '无': 'no', '是': 'is', '否': 'not',
            '能': 'can', '不': 'not', '很': 'very', '较': 'quite',
            '轻': 'mild', '中': 'moderate', '重': 'severe',
            '第': 'number', '次': 'time', '个': 'unit', '项': 'item',
            '种': 'type', '例': 'case', '位': 'position', '处': 'place',
        }
        
        result = []
        for char in text:
            if char in char_map:
                result.append(char_map[char])
            elif char.isalnum():
                result.append(char)
            # 忽略其他字符
        
        if result:
            return '_'.join(result)
        else:
            # 如果无法转换，使用默认名称
            return 'custom_field'
    
    def _normalize_field_name(self, name: str) -> str:
        """规范化字段名称"""
        if not name:
            return 'field'
        
        # 转换为小写
        name = name.lower()
        
        # 替换非法字符为下划线
        name = re.sub(r'[^a-z0-9_]', '_', name)
        
        # 移除连续的下划线
        name = re.sub(r'_+', '_', name)
        
        # 移除开头和结尾的下划线
        name = name.strip('_')
        
        # 确保以字母开头
        if name and name[0].isdigit():
            name = 'field_' + name
        
        # 如果为空，使用默认名称
        if not name:
            name = 'field'
        
        return name
    
    def _handle_duplicates(self, base_name: str, existing_names: List[str]) -> str:
        """处理重复的字段名称"""
        if base_name not in existing_names:
            return base_name
        
        # 添加数字后缀
        counter = 2
        while f"{base_name}_{counter}" in existing_names:
            counter += 1
        
        return f"{base_name}_{counter}"
    
    def batch_generate_field_names(self, field_labels: List[str]) -> Dict[str, str]:
        """
        批量生成字段名称
        
        Args:
            field_labels: 字段标签列表
            
        Returns:
            字段标签到字段名称的映射字典
        """
        result = {}
        existing_names = []
        
        for label in field_labels:
            field_name = self.generate_field_name(label, existing_names)
            result[label] = field_name
            existing_names.append(field_name)
        
        return result


# 创建全局实例
field_name_generator = FieldNameGenerator()
