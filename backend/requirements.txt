# 核心框架
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
Flask-Bcrypt==1.0.1

# 数据库驱动
PyMySQL==1.1.0
cryptography==41.0.7

# 配置和工具
python-dotenv==1.0.0
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0
APScheduler==3.10.4
python-dateutil==2.8.2

# 数据导出功能 (新增)
# 注意: csv 和 io 是Python标准库，无需安装
# 如需Excel格式导出，可选择以下依赖之一：
# openpyxl==3.1.2  # 用于Excel文件读写
# xlsxwriter==3.1.9  # 用于Excel文件写入
# pandas==2.1.3  # 用于数据处理和导出 (可选，较重)

# 统计和分析功能 (新增)
# 当前使用SQLAlchemy原生查询，如需更复杂统计可添加：
# sqlalchemy-utils==0.41.1  # SQLAlchemy工具扩展

# 测试依赖
pytest==7.4.3
pytest-flask==1.3.0
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0
