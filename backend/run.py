import os
from app import create_app, db
from app.models import User, Department, FollowupTemplate, FollowupRecord

app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    """Shell上下文处理器"""
    return dict(db=db, User=User, Department=Department, 
                FollowupTemplate=FollowupTemplate, FollowupRecord=FollowupRecord)

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成')

@app.cli.command()
def create_admin():
    """创建超级管理员"""
    from app.models import User
    admin = User(
        username='admin',
        email='<EMAIL>',
        role='super_admin',
        is_active=True
    )
    admin.set_password('admin123')
    db.session.add(admin)
    db.session.commit()
    print('超级管理员创建成功: admin/admin123')

if __name__ == '__main__':
    print("正在启动医院回访系统后端服务...")
    print("服务地址: http://localhost:5001")
    print("调试模式: 开启")
    app.run(debug=True, host='0.0.0.0', port=5001)
