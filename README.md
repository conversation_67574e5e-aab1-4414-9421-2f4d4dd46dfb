# 🏥 医院回访系统

一个基于Flask + React的医院患者回访管理系统。

## 🚀 快速启动

### 后端启动
```bash
cd backend
python run.py
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 📋 系统要求

- **Python 3.8+**
- **MySQL 5.7+** （数据库）
- **Node.js 16+** （仅前端开发需要）

## 🔧 安装依赖

### 后端依赖
```bash
cd backend
pip install -r requirements.txt
```

### 前端依赖（可选）
```bash
cd frontend
npm install
```

## 🌐 访问地址

- **后端API**: http://localhost:5001
- **前端应用**: http://localhost:3000 （如启动前端）

## 📁 项目结构

```
hospital-followup/
├── backend/                    # Flask后端API
│   ├── app/                   # 应用代码
│   ├── config.py             # 配置文件
│   ├── requirements.txt      # Python依赖
│   └── run.py               # 启动文件
├── frontend/                  # React前端
│   ├── src/                  # 源代码
│   ├── package.json         # Node.js依赖
│   └── vite.config.js       # Vite配置
├── deploy.sh                 # Linux部署脚本
├── update.sh                 # 更新脚本
└── README.md                # 本文件
```

## 🎯 功能特性

- 👥 **用户管理**: 支持多角色用户管理
- 🏥 **科室管理**: 科室信息维护和权限隔离
- 📋 **患者管理**: 患者信息录入和模板化管理
- 📞 **回访管理**: 回访计划制定和执行跟踪
- 📊 **模板管理**: 灵活的患者信息和回访模板配置
- ✨ **智能输入**: 字段录入历史记录和智能建议

## 🐧 Linux部署

### 自动化部署
```bash
# 克隆项目
git clone https://gitee.com/jiang-linlinlin/hospital-followup-system.git
cd hospital-followup-system

# 一键部署
chmod +x deploy.sh
./deploy.sh
```

### 更新系统
```bash
# 更新到最新版本
./update.sh

# 回滚到上一版本
./update.sh rollback
```

## 📞 默认账户

- 用户名: `admin`
- 密码: `admin123`

## 💡 常见问题

### 端口被占用
```bash
# Windows
netstat -ano | findstr :5001
taskkill /PID <进程ID> /F

# Linux
lsof -i :5001
kill -9 <PID>
```

### 前端构建问题
```bash
cd frontend
rm -rf node_modules
npm install
npm run build
```

---

🎯 **医院回访系统 - 提升患者回访管理效率**
