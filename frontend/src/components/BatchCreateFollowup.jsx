import React, { useState, useEffect } from 'react'
import { 
  Modal, 
  Form, 
  Select, 
  InputNumber, 
  Button, 
  Steps, 
  Card, 
  Statistic, 
  Table, 
  message, 
  Alert,
  Space,
  Divider
} from 'antd'
import { useQuery } from 'react-query'
import { followupService } from '../services/followups'
import api from '../services/api'

const { Step } = Steps
const { Option } = Select

const BatchCreateFollowup = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm()
  const [currentStep, setCurrentStep] = useState(0)
  const [previewData, setPreviewData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [formValues, setFormValues] = useState(null)

  // 获取患者模板列表
  const { data: patientTemplatesData } = useQuery(
    'patient-templates',
    () => api.get('/patient-templates', { params: { per_page: 100 } })
  )

  // 获取回访模板列表
  const { data: followupTemplatesData } = useQuery(
    'followup-templates',
    () => api.get('/templates', { params: { per_page: 100 } })
  )

  const patientTemplates = patientTemplatesData?.templates || []
  const followupTemplates = followupTemplatesData?.templates || []

  // 重置表单和状态
  const resetForm = () => {
    form.resetFields()
    setCurrentStep(0)
    setPreviewData(null)
    setFormValues(null)
  }

  // 处理取消
  const handleCancel = () => {
    resetForm()
    onCancel()
  }

  // 预览批量创建
  const handlePreview = async () => {
    try {
      const values = await form.validateFields()
      setFormValues(values) // 保存表单值
      setLoading(true)

      const response = await followupService.batchPreviewFollowups({
        patient_template_id: values.patient_template_id,
        followup_template_id: values.followup_template_id
      })

      setPreviewData(response)
      setCurrentStep(1)
    } catch (error) {
      console.error('预览失败:', error)
      message.error(error.response?.data?.error || '预览失败')
    } finally {
      setLoading(false)
    }
  }

  // 执行批量创建
  const handleCreate = async () => {
    try {
      // 使用保存的表单值，不重新验证
      if (!formValues) {
        message.error('表单数据丢失，请重新操作')
        return
      }

      setLoading(true)

      const response = await followupService.batchCreateFollowups({
        patient_template_id: formValues.patient_template_id,
        followup_template_id: formValues.followup_template_id,
        days_after: formValues.days_after
      })

      message.success(
        `批量创建完成！创建了 ${response.created_count} 条回访记录，跳过了 ${response.skipped_count} 条已存在的记录`
      )

      resetForm()
      onSuccess()
    } catch (error) {
      console.error('批量创建失败:', error)
      message.error(error.response?.data?.error || '批量创建失败')
    } finally {
      setLoading(false)
    }
  }

  // 上一步
  const handlePrevious = () => {
    setCurrentStep(0)
    setPreviewData(null)
    // 保留formValues，这样用户可以修改表单后重新预览
  }

  // 患者列表列配置
  const patientColumns = [
    {
      title: '患者姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '录入时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleDateString() : '-'
    },
    {
      title: '回访状态',
      dataIndex: 'has_followup',
      key: 'has_followup',
      render: (hasFollowup) => (
        <span style={{ color: hasFollowup ? '#ff4d4f' : '#52c41a' }}>
          {hasFollowup ? '已有回访' : '待创建'}
        </span>
      )
    }
  ]

  return (
    <Modal
      title="批量创建回访记录"
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={null}
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="选择模板" description="选择患者模板和回访设置" />
        <Step title="预览确认" description="确认要创建的回访记录" />
      </Steps>

      {currentStep === 0 && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handlePreview}
        >
          <Alert
            message="批量创建说明"
            description="选择患者模板后，系统会为所有使用该模板录入的患者创建回访记录。已有回访记录的患者将自动跳过。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form.Item
            label="患者模板"
            name="patient_template_id"
            rules={[{ required: true, message: '请选择患者模板' }]}
          >
            <Select
              placeholder="请选择患者模板"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {patientTemplates.map(template => (
                <Option key={template.id} value={template.id}>
                  {template.name} ({template.department_name})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="回访模板"
            name="followup_template_id"
            rules={[{ required: true, message: '请选择回访模板' }]}
          >
            <Select
              placeholder="请选择回访模板"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {followupTemplates.map(template => (
                <Option key={template.id} value={template.id}>
                  {template.name} ({template.followup_method})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="回访间隔天数"
            name="days_after"
            rules={[
              { required: true, message: '请输入回访间隔天数' },
              { type: 'number', min: 1, max: 365, message: '天数必须在1-365之间' }
            ]}
            extra="从患者录入日期开始计算，多少天后进行回访"
          >
            <InputNumber
              placeholder="请输入天数"
              style={{ width: '100%' }}
              min={1}
              max={365}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                下一步：预览
              </Button>
            </Space>
          </Form.Item>
        </Form>
      )}

      {currentStep === 1 && previewData && (
        <div>
          <Alert
            message="预览信息"
            description={`找到 ${previewData.total_patients} 个使用"${previewData.patient_template.name}"模板录入的患者`}
            type="success"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <div style={{ marginBottom: 24 }}>
            <Space size="large">
              <Statistic
                title="总患者数"
                value={previewData.total_patients}
                valueStyle={{ color: '#1890ff' }}
              />
              <Statistic
                title="待创建回访"
                value={previewData.patients_without_followup}
                valueStyle={{ color: '#52c41a' }}
              />
              <Statistic
                title="已有回访"
                value={previewData.patients_with_followup}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Space>
          </div>

          <Divider>患者列表预览（前10条）</Divider>

          <Table
            columns={patientColumns}
            dataSource={previewData.patients}
            rowKey="id"
            pagination={false}
            size="small"
            style={{ marginBottom: 24 }}
          />

          <Form.Item>
            <Space>
              <Button onClick={handlePrevious}>
                上一步
              </Button>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button 
                type="primary" 
                onClick={handleCreate} 
                loading={loading}
                disabled={previewData.patients_without_followup === 0}
              >
                确认创建 ({previewData.patients_without_followup} 条)
              </Button>
            </Space>
          </Form.Item>
        </div>
      )}
    </Modal>
  )
}

export default BatchCreateFollowup
