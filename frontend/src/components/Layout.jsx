import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Button,
  theme,
  Tag,
  Badge,
  Drawer
} from 'antd'
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  FileTextOutlined,
  ContactsOutlined,
  PhoneOutlined,
  LogoutOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  CrownOutlined,
  SafetyCertificateOutlined,
  IdcardOutlined
} from '@ant-design/icons'
import { authService } from '../services/auth'

const { Header, Sider, Content } = AntLayout
const { Title } = Typography

const Layout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { token } = theme.useToken()

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
      if (window.innerWidth <= 768) {
        setCollapsed(true)
      }
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 获取当前用户信息
  const { data: currentUser } = useQuery('currentUser', authService.getCurrentUser, {
    staleTime: 5 * 60 * 1000, // 5分钟内数据保持新鲜
    cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
    retry: false,
    onError: (error) => {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，跳转到登录页
      authService.logout()
    }
  })

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/patients',
      icon: <ContactsOutlined />,
      label: '患者管理',
    },
    {
      key: '/followups',
      icon: <PhoneOutlined />,
      label: '回访管理',
    },
    {
      key: '/templates',
      icon: <FileTextOutlined />,
      label: '回访模板',
    },
    {
      key: '/patient-templates',
      icon: <FileTextOutlined />,
      label: '患者信息模板',
    },
  ]

  // 暂时显示所有菜单项（后续可以根据用户角色动态调整）
  menuItems.push(
    {
      key: '/departments',
      icon: <TeamOutlined />,
      label: '科室管理',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
    }
  )

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <SettingOutlined />,
      label: '个人设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ]

  const handleMenuClick = ({ key }) => {
    navigate(key)
    // 移动端点击菜单后关闭抽屉
    if (isMobile) {
      setMobileMenuVisible(false)
    }
  }

  const handleUserMenuClick = ({ key }) => {
    if (key === 'logout') {
      authService.logout()
    } else if (key === 'profile') {
      // TODO: 打开个人设置弹窗
    }
  }

  const getRoleName = (role) => {
    const roleMap = {
      'super_admin': '超级管理员',
      'dept_admin': '科室管理员',
      'user': '普通用户'
    }
    return roleMap[role] || role
  }

  const getRoleIcon = (role) => {
    const iconMap = {
      'super_admin': <CrownOutlined style={{ color: '#ff4d4f' }} />,
      'dept_admin': <SafetyCertificateOutlined style={{ color: '#1890ff' }} />,
      'user': <IdcardOutlined style={{ color: '#52c41a' }} />
    }
    return iconMap[role] || <UserOutlined />
  }

  const getRoleColor = (role) => {
    const colorMap = {
      'super_admin': 'red',
      'dept_admin': 'blue',
      'user': 'green'
    }
    return colorMap[role] || 'default'
  }

  // 渲染菜单内容
  const renderMenu = () => (
    <>
      <div style={{
        height: 64,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottom: `1px solid ${token.colorBorder}`,
        padding: '0 16px'
      }}>
        <Title level={4} style={{ margin: 0, color: token.colorPrimary }}>
          {(collapsed && !isMobile) ? '回访' : '医院回访系统'}
        </Title>
      </div>

      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ borderRight: 0 }}
      />
    </>
  )

  return (
    <AntLayout className="main-layout">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          style={{
            background: token.colorBgContainer,
          }}
        >
          {renderMenu()}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          title="医院回访系统"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          styles={{ body: { padding: 0 } }}
          width={280}
        >
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ borderRight: 0 }}
          />
        </Drawer>
      )}

      <AntLayout>
        <Header className="main-header" style={{
          background: token.colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: isMobile ? '0 16px' : '0 24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => {
              if (isMobile) {
                setMobileMenuVisible(true)
              } else {
                setCollapsed(!collapsed)
              }
            }}
            style={{
              fontSize: '16px',
              width: isMobile ? 48 : 64,
              height: isMobile ? 48 : 64,
            }}
          />

          {/* 中间显示用户科室和角色信息 - 移动端简化显示 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
            overflow: 'hidden'
          }}>
            {currentUser && (
              <Space size={isMobile ? "small" : "middle"} style={{
                fontSize: isMobile ? '12px' : '14px',
                maxWidth: '100%'
              }}>
                {!isMobile && (
                  <Avatar
                    size="small"
                    style={{
                      backgroundColor: token.colorPrimary,
                      fontSize: '14px'
                    }}
                    icon={getRoleIcon(currentUser.role)}
                  />
                )}

                <span style={{
                  fontWeight: '500',
                  color: '#262626',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {currentUser.real_name || currentUser.username}
                </span>

                {!isMobile && <span style={{ color: '#8c8c8c' }}>|</span>}

                <span style={{
                  color: '#595959',
                  fontSize: isMobile ? '11px' : '13px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {currentUser.department_name || '未分配科室'}
                </span>

                <Tag
                  color={getRoleColor(currentUser.role)}
                  size="small"
                  style={{
                    margin: 0,
                    fontSize: isMobile ? '10px' : '11px',
                    lineHeight: isMobile ? '16px' : '18px',
                    height: isMobile ? '18px' : '20px'
                  }}
                >
                  {getRoleName(currentUser.role)}
                </Tag>
              </Space>
            )}
          </div>

          <Space>
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  icon={<UserOutlined />}
                  size={isMobile ? "small" : "default"}
                />
                {!isMobile && (
                  <span>{currentUser?.real_name || currentUser?.username || '用户'}</span>
                )}
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content className="main-content">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
