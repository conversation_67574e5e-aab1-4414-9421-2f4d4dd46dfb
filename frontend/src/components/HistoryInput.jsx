import React, { useState, useEffect, useCallback } from 'react'
import { AutoComplete, Input } from 'antd'
import api from '../services/api'

const { TextArea } = Input

// 简单的防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 带历史记录的输入组件
 * @param {Object} props
 * @param {string} props.fieldName - 字段名称
 * @param {string} props.templateType - 模板类型 (patient/followup)
 * @param {number} props.templateId - 模板ID
 * @param {string} props.type - 输入类型 (text/textarea)
 * @param {string} props.placeholder - 占位符
 * @param {Object} props.style - 样式
 * @param {Function} props.onChange - 值变化回调
 * @param {string} props.value - 当前值
 * @param {number} props.rows - TextArea行数
 */
const HistoryInput = ({
  fieldName,
  templateType,
  templateId,
  type = 'text',
  placeholder,
  style,
  onChange,
  value,
  rows = 3,
  ...restProps
}) => {
  const [suggestions, setSuggestions] = useState([])
  const [loading, setLoading] = useState(false)

  // 调试日志
  console.log('HistoryInput 渲染:', { fieldName, templateType, templateId, value })

  // 获取建议的防抖函数
  const debouncedGetSuggestions = useCallback(
    debounce(async (query) => {
      if (!fieldName || !templateType) {
        console.log('HistoryInput: 缺少必要参数', { fieldName, templateType })
        return
      }

      console.log('HistoryInput: 获取建议', { fieldName, templateType, query })

      try {
        setLoading(true)
        const response = await api.get('/field-history/suggestions', {
          params: {
            field_name: fieldName,
            template_type: templateType,
            query: query || '',
            limit: 10
          }
        })

        console.log('HistoryInput: 获取建议成功', response)
        setSuggestions(response.suggestions || [])
      } catch (error) {
        console.error('获取历史建议失败:', error)
        setSuggestions([])
      } finally {
        setLoading(false)
      }
    }, 300),
    [fieldName, templateType]
  )

  // 记录字段使用历史
  const recordUsage = async (fieldValue) => {
    if (!fieldValue || !fieldValue.trim() || !fieldName || !templateType) {
      console.log('HistoryInput: 跳过记录', { fieldValue, fieldName, templateType })
      return
    }

    console.log('HistoryInput: 记录使用', { fieldName, fieldValue: fieldValue.trim(), templateType, templateId })

    try {
      const response = await api.post('/field-history/record', {
        field_name: fieldName,
        field_value: fieldValue.trim(),
        template_type: templateType,
        template_id: templateId
      })
      console.log('HistoryInput: 记录成功', response)
    } catch (error) {
      console.error('记录字段历史失败:', error)
    }
  }

  // 初始加载建议
  useEffect(() => {
    console.log('HistoryInput useEffect:', { fieldName, templateType })
    if (fieldName && templateType) {
      debouncedGetSuggestions('')
    }
  }, [fieldName, templateType, debouncedGetSuggestions])

  // 处理值变化
  const handleChange = (newValue) => {
    console.log('HistoryInput: 值变化', { fieldName, newValue })
    onChange?.(newValue)

    // 当用户输入时，获取新的建议
    if (typeof newValue === 'string') {
      debouncedGetSuggestions(newValue)
    }
  }

  // 处理选择建议
  const handleSelect = (selectedValue) => {
    console.log('HistoryInput: 选择建议', selectedValue)
    onChange?.(selectedValue)
    recordUsage(selectedValue)
  }

  // 处理失去焦点，记录使用历史
  const handleBlur = () => {
    if (value && value.trim()) {
      recordUsage(value.trim())
    }
  }

  // 准备选项数据
  const options = suggestions.map(suggestion => ({
    value: suggestion,
    label: suggestion
  }))

  // 如果是多行文本
  if (type === 'textarea') {
    return (
      <AutoComplete
        value={value}
        options={options}
        onSelect={handleSelect}
        onSearch={debouncedGetSuggestions}
        onChange={handleChange}
        style={style}
        {...restProps}
      >
        <TextArea
          rows={rows}
          placeholder={placeholder}
          onBlur={handleBlur}
          style={style}
        />
      </AutoComplete>
    )
  }

  // 单行文本
  return (
    <AutoComplete
      value={value}
      options={options}
      onSelect={handleSelect}
      onSearch={debouncedGetSuggestions}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      style={style}
      {...restProps}
    >
      <Input
        placeholder={placeholder}
        style={style}
      />
    </AutoComplete>
  )
}

export default HistoryInput
