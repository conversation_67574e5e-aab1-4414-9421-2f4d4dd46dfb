import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Card,
  Avatar,
  FloatButton
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  KeyOutlined
} from '@ant-design/icons'
import { userService } from '../services/users'
import { departmentService } from '../services/departments'

const { Title } = Typography
const { Option } = Select

const Users = () => {
  const [form] = Form.useForm()
  const [resetForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isResetModalVisible, setIsResetModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState(null)
  const [resetUserId, setResetUserId] = useState(null)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })
  const [filters, setFilters] = useState({})
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 获取用户列表
  const { data: usersData, isLoading, refetch } = useQuery(
    ['users', pagination.current, pagination.pageSize, filters],
    () => userService.getUsers({
      page: pagination.current,
      per_page: pagination.pageSize,
      ...filters
    }),
    {
      keepPreviousData: true,
    }
  )

  // 获取科室列表
  const { data: departmentsData } = useQuery(
    'departments',
    async () => {
      const response = await departmentService.getDepartments({ per_page: 100 })
      return response
    }
  )

  // 创建/更新用户
  const userMutation = useMutation(
    (userData) => {
      if (editingUser) {
        return userService.updateUser(editingUser.id, userData)
      } else {
        return userService.createUser(userData)
      }
    },
    {
      onSuccess: () => {
        message.success(editingUser ? '用户更新成功' : '用户创建成功')
        setIsModalVisible(false)
        setEditingUser(null)
        form.resetFields()
        queryClient.invalidateQueries('users')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 删除用户
  const deleteMutation = useMutation(
    (userId) => userService.deleteUser(userId),
    {
      onSuccess: () => {
        message.success('用户删除成功')
        queryClient.invalidateQueries('users')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 重置密码
  const resetPasswordMutation = useMutation(
    ({ userId, passwordData }) => userService.resetPassword(userId, passwordData),
    {
      onSuccess: (data) => {
        message.success(`密码重置成功，新密码：${data.new_password}`)
        setIsResetModalVisible(false)
        setResetUserId(null)
        resetForm.resetFields()
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '姓名',
      dataIndex: 'real_name',
      key: 'real_name',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => {
        const roleMap = {
          'super_admin': { text: '超级管理员', color: 'red' },
          'dept_admin': { text: '科室管理员', color: 'blue' },
          'user': { text: '普通用户', color: 'green' }
        }
        const roleInfo = roleMap[role] || { text: role, color: 'default' }
        return <Tag color={roleInfo.color}>{roleInfo.text}</Tag>
      }
    },
    {
      title: '科室',
      dataIndex: 'department_name',
      key: 'department_name',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => handleResetPassword(record)}
          >
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleEdit = (user) => {
    setEditingUser(user)
    form.setFieldsValue(user)
    setIsModalVisible(true)
  }

  const handleResetPassword = (user) => {
    setResetUserId(user.id)
    setIsResetModalVisible(true)
  }

  const handleSubmit = (values) => {
    userMutation.mutate(values)
  }

  const handleResetPasswordSubmit = (values) => {
    resetPasswordMutation.mutate({
      userId: resetUserId,
      passwordData: values
    })
  }

  const handleTableChange = (newPagination, tableFilters) => {
    setPagination(newPagination)
    // 处理筛选条件
    const newFilters = {}
    if (tableFilters.role && tableFilters.role.length > 0) {
      newFilters.role = tableFilters.role[0]
    }
    setFilters(newFilters)
  }

  // 移动端卡片视图渲染函数
  const renderMobileCard = (user) => {
    const getRoleInfo = (role) => {
      const roleMap = {
        'super_admin': { text: '超级管理员', color: 'red' },
        'dept_admin': { text: '科室管理员', color: 'blue' },
        'user': { text: '普通用户', color: 'green' }
      }
      return roleMap[role] || { text: role, color: 'default' }
    }

    const roleInfo = getRoleInfo(user.role)

    return (
      <Card
        key={user.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {user.real_name || user.username}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                @{user.username} | ID: {user.id}
              </div>
            </div>
          </div>
          <div style={{ display: 'flex', gap: 4, flexDirection: 'column', alignItems: 'flex-end' }}>
            <Tag color={roleInfo.color} size="small">
              {roleInfo.text}
            </Tag>
            <Tag color={user.is_active ? 'green' : 'red'} size="small">
              {user.is_active ? '启用' : '禁用'}
            </Tag>
          </div>
        </div>

        <div className="mobile-card-content">
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {user.email && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <MailOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>{user.email}</span>
              </div>
            )}
            {user.phone && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <PhoneOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>{user.phone}</span>
              </div>
            )}
            {user.department_name && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>科室:</span>
                <span style={{ fontSize: 14 }}>{user.department_name}</span>
              </div>
            )}
            {user.created_at && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>创建:</span>
                <span style={{ fontSize: 14 }}>{new Date(user.created_at).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>

        <div className="mobile-card-actions">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(user)}
          >
            编辑
          </Button>
          <Button
            size="small"
            icon={<KeyOutlined />}
            onClick={() => handleResetPassword(user)}
          >
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => deleteMutation.mutate(user.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 桌面端页面头部 */}
      {!isMobile && (
        <div className="page-header">
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} className="page-title">用户管理</Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingUser(null)
                    form.resetFields()
                    setIsModalVisible(true)
                  }}
                >
                  新增用户
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      )}

      {/* 桌面端表格 */}
      {!isMobile && (
        <Table
          columns={columns}
          dataSource={usersData?.users || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: usersData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              用户管理 ({usersData?.total || 0})
            </Title>

            {isLoading ? (
              <Card loading style={{ marginBottom: 12 }} />
            ) : (
              <>
                {usersData?.users && usersData.users.length > 0 ? (
                  usersData.users.map(renderMobileCard)
                ) : (
                  <Card style={{ textAlign: 'center', marginBottom: 12 }}>
                    <div style={{ padding: '20px 0', color: '#8c8c8c' }}>
                      暂无用户数据
                    </div>
                  </Card>
                )}

                {/* 移动端分页 */}
                {usersData?.total > pagination.pageSize && (
                  <div style={{ textAlign: 'center', marginTop: 16, marginBottom: 80 }}>
                    <Button
                      disabled={pagination.current === 1}
                      onClick={() => setPagination({ ...pagination, current: pagination.current - 1 })}
                      style={{ marginRight: 8 }}
                    >
                      上一页
                    </Button>
                    <span style={{ margin: '0 16px', color: '#8c8c8c' }}>
                      {pagination.current} / {Math.ceil(usersData.total / pagination.pageSize)}
                    </span>
                    <Button
                      disabled={pagination.current >= Math.ceil(usersData.total / pagination.pageSize)}
                      onClick={() => setPagination({ ...pagination, current: pagination.current + 1 })}
                    >
                      下一页
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => {
            setEditingUser(null)
            form.resetFields()
            setIsModalVisible(true)
          }}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 新增/编辑用户弹窗 */}
      <Modal
        title={editingUser ? '编辑用户' : '新增用户'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingUser(null)
          form.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 600}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input disabled={!!editingUser} />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="real_name"
                label="姓名"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="phone"
                label="电话"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码长度不能少于6位' }
              ]}
            >
              <Input.Password />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select>
                  <Option value="user">普通用户</Option>
                  <Option value="dept_admin">科室管理员</Option>
                  <Option value="super_admin">超级管理员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="department_id"
                label="科室"
              >
                <Select allowClear placeholder="请选择科室">
                  {departmentsData?.departments?.map(dept => (
                    <Option key={dept.id} value={dept.id}>
                      {dept.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={userMutation.isLoading}
              >
                {editingUser ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false)
                  setEditingUser(null)
                  form.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        title="重置密码"
        open={isResetModalVisible}
        onCancel={() => {
          setIsResetModalVisible(false)
          setResetUserId(null)
          resetForm.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 400}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={resetForm}
          layout="vertical"
          onFinish={handleResetPasswordSubmit}
        >
          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度不能少于6位' }
            ]}
            initialValue="123456"
          >
            <Input.Password />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={resetPasswordMutation.isLoading}
              >
                重置密码
              </Button>
              <Button
                onClick={() => {
                  setIsResetModalVisible(false)
                  setResetUserId(null)
                  resetForm.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Users
