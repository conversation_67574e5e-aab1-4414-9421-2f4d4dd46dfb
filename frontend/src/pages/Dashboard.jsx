import React, { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import { Row, Col, Card, Statistic, Typography, List, Tag, Space, Button } from 'antd'
import {
  UserOutlined,
  TeamOutlined,
  PhoneOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import api from '../services/api'

const { Title } = Typography

const Dashboard = () => {
  const navigate = useNavigate()
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 获取统计数据
  const { data: stats, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    async () => {
      const [patientStats, followupStats, templates, departments] = await Promise.all([
        api.get('/patients/stats'),
        api.get('/followups/stats'),
        api.get('/templates', { params: { per_page: 1 } }),
        api.get('/departments', { params: { per_page: 1 } })
      ])

      return {
        // 患者统计
        totalPatients: patientStats.total_patients || 0,
        todayPatients: patientStats.today_patients || 0,
        weekPatients: patientStats.week_patients || 0,
        monthPatients: patientStats.month_patients || 0,

        // 回访统计
        totalFollowups: followupStats.total_followups || 0,
        completedFollowups: followupStats.completed_followups || 0,
        pendingFollowups: followupStats.pending_followups || 0,
        overdueFollowups: followupStats.overdue_followups || 0,
        todayFollowups: followupStats.today_followups || 0,
        weekFollowups: followupStats.week_followups || 0,
        monthFollowups: followupStats.month_followups || 0,

        // 其他统计
        templates: templates.total || 0,
        departments: departments.total || 0
      }
    },
    {
      refetchInterval: 30000, // 30秒刷新一次
    }
  )

  // 获取待处理回访
  const { data: pendingFollowups } = useQuery(
    'pendingFollowups',
    async () => {
      const response = await api.get('/followups', {
        params: { status: 'pending', per_page: 10 }
      })
      return response.followups || []
    }
  )

  // 获取今日回访
  const { data: todayFollowups } = useQuery(
    'todayFollowups',
    async () => {
      const today = new Date().toISOString().split('T')[0]
      const response = await api.get('/followups', {
        params: { 
          date_from: today,
          date_to: today,
          per_page: 10
        }
      })
      return response.followups || []
    }
  )

  const getStatusTag = (status, isOverdue) => {
    if (isOverdue) {
      return <Tag color="red" icon={<ExclamationCircleOutlined />}>已逾期</Tag>
    }
    
    const statusMap = {
      pending: <Tag color="orange" icon={<ClockCircleOutlined />}>待回访</Tag>,
      completed: <Tag color="green" icon={<CheckCircleOutlined />}>已完成</Tag>,
      failed: <Tag color="red">失败</Tag>,
      cancelled: <Tag color="default">已取消</Tag>
    }
    return statusMap[status] || <Tag>{status}</Tag>
  }

  return (
    <div>
      <div className="page-header">
        <Title level={3} className="page-title">仪表盘</Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={isMobile ? [8, 8] : [16, 16]} style={{ marginBottom: isMobile ? 16 : 24 }}>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="患者总数"
              value={stats?.totalPatients || 0}
              prefix={<UserOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '20px' : '24px', color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="已完成回访"
              value={stats?.completedFollowups || 0}
              prefix={<CheckCircleOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '20px' : '24px', color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="待回访人数"
              value={stats?.pendingFollowups || 0}
              prefix={<ClockCircleOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '20px' : '24px', color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="逾期回访"
              value={stats?.overdueFollowups || 0}
              prefix={<ExclamationCircleOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '20px' : '24px', color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 第二行统计卡片 */}
      <Row gutter={isMobile ? [8, 8] : [16, 16]} style={{ marginBottom: isMobile ? 16 : 24 }}>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="今日回访"
              value={stats?.todayFollowups || 0}
              prefix={<PhoneOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '18px' : '20px', color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="本周新增患者"
              value={stats?.weekPatients || 0}
              prefix={<UserOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '18px' : '20px', color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="回访模板"
              value={stats?.templates || 0}
              prefix={<FileTextOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '18px' : '20px', color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} lg={6}>
          <Card size={isMobile ? "small" : "default"}>
            <Statistic
              title="科室数量"
              value={stats?.departments || 0}
              prefix={<TeamOutlined />}
              loading={statsLoading}
              valueStyle={{ fontSize: isMobile ? '18px' : '20px', color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={isMobile ? [8, 8] : [16, 16]}>
        {/* 待处理回访 */}
        <Col xs={24} lg={12}>
          <Card
            title="待处理回访"
            size={isMobile ? "small" : "default"}
            extra={
              <Button
                type="link"
                size={isMobile ? "small" : "default"}
                onClick={() => navigate('/followups?status=pending')}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={pendingFollowups}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{item.patient_name}</span>
                        {getStatusTag(item.status, item.is_overdue)}
                      </Space>
                    }
                    description={
                      <div>
                        <div>回访时间: {new Date(item.followup_date).toLocaleString()}</div>
                        <div>模板: {item.template_name}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无待处理回访' }}
            />
          </Card>
        </Col>

        {/* 今日回访 */}
        <Col xs={24} lg={12}>
          <Card
            title="今日回访"
            extra={
              <Button 
                type="link" 
                onClick={() => {
                  const today = new Date().toISOString().split('T')[0]
                  navigate(`/followups?date_from=${today}&date_to=${today}`)
                }}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={todayFollowups}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{item.patient_name}</span>
                        {getStatusTag(item.status, item.is_overdue)}
                      </Space>
                    }
                    description={
                      <div>
                        <div>回访时间: {new Date(item.followup_date).toLocaleString()}</div>
                        <div>执行人: {item.executor_name || '未分配'}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '今日暂无回访' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
