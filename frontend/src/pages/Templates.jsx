import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Card,
  InputNumber,
  TimePicker,
  Checkbox,
  Spin,
  Divider,
  FloatButton,
  Avatar,
  Badge,
  Radio,
  Alert
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  SettingOutlined,
  MinusCircleOutlined,
  FileTextOutlined,
  TeamOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { templateService } from '../services/templates'
import { departmentService } from '../services/departments'
import { authService } from '../services/auth'
import api from '../services/api'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const Templates = () => {
  const [form] = Form.useForm()
  const [fieldForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isFieldModalVisible, setIsFieldModalVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [templateFields, setTemplateFields] = useState([])
  const [editingFieldIndex, setEditingFieldIndex] = useState(-1)
  const [previewData, setPreviewData] = useState(null)
  const [isPreviewLoading, setIsPreviewLoading] = useState(false)
  const [showAdvancedMode, setShowAdvancedMode] = useState(false)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })

  // 获取模板列表
  const { data: templatesData, isLoading, refetch } = useQuery(
    ['templates', pagination.current, pagination.pageSize],
    () => templateService.getTemplates({
      page: pagination.current,
      per_page: pagination.pageSize
    }),
    {
      keepPreviousData: true,
    }
  )

  // 获取当前用户信息
  const { data: currentUser } = useQuery('currentUser', authService.getCurrentUser)

  // 获取科室列表（仅超级管理员需要）
  const { data: departmentsData } = useQuery(
    'departments',
    async () => {
      const response = await departmentService.getDepartments({ per_page: 100 })
      return response
    },
    {
      enabled: currentUser?.role === 'super_admin' // 只有超级管理员才获取科室列表
    }
  )

  // 创建/更新模板
  const templateMutation = useMutation(
    (templateData) => {
      const data = {
        ...templateData,
        fields: showAdvancedMode ? templateFields : undefined, // 简单模式下不传字段，让后端自动生成
        followup_schedule: showAdvancedMode ? {
          days: templateData.followup_days || [1, 3, 7],
          time: templateData.followup_time ? templateData.followup_time.format('HH:mm') : '09:00'
        } : undefined, // 简单模式下不传时间安排，让后端自动生成
        followup_method: showAdvancedMode ? templateData.followup_method : undefined // 简单模式下不传回访方式，让后端自动生成
      }

      if (editingTemplate) {
        return templateService.updateTemplate(editingTemplate.id, data)
      } else {
        return templateService.createTemplate(data)
      }
    },
    {
      onSuccess: () => {
        message.success(editingTemplate ? '模板更新成功' : '模板创建成功')
        setIsModalVisible(false)
        setEditingTemplate(null)
        setTemplateFields([])
        setPreviewData(null)
        setShowAdvancedMode(false)
        form.resetFields()
        queryClient.invalidateQueries('templates')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 删除模板
  const deleteMutation = useMutation(
    (templateId) => templateService.deleteTemplate(templateId),
    {
      onSuccess: () => {
        message.success('模板删除成功')
        queryClient.invalidateQueries('templates')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 复制模板
  const copyMutation = useMutation(
    ({ templateId, name }) => templateService.copyTemplate(templateId, { name }),
    {
      onSuccess: () => {
        message.success('模板复制成功')
        queryClient.invalidateQueries('templates')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 预览回访模板字段和配置
  const handlePreviewTemplate = async (templateName) => {
    if (!templateName) {
      setPreviewData(null)
      return
    }

    setIsPreviewLoading(true)
    try {
      const response = await api.post('/templates/preview-fields', {
        name: templateName
      })
      setPreviewData(response)
    } catch (error) {
      console.error('预览模板失败:', error)
      message.error('预览模板失败')
      setPreviewData(null)
    } finally {
      setIsPreviewLoading(false)
    }
  }

  // 生成字段名称（与PatientTemplates.jsx中的函数相同）
  const generateFieldName = (fieldLabel, existingFields = []) => {
    if (!fieldLabel) return ''

    // 常用医疗术语中英文对照表
    const medicalTerms = {
      '姓名': 'name',
      '性别': 'gender',
      '年龄': 'age',
      '联系电话': 'phone',
      '地址': 'address',
      '联系状态': 'contact_status',
      '回访时间': 'followup_time',
      '回访方式': 'followup_method',
      '回访结果': 'followup_result',
      '总体情况': 'overall_condition',
      '满意度': 'satisfaction',
      '建议': 'suggestion',
      '备注': 'notes',
      '下次回访': 'next_followup',
      '症状改善': 'symptom_improvement',
      '康复情况': 'recovery_status',
      '母乳喂养情况': 'breastfeeding_status',
      '伤口愈合情况': 'wound_healing',
      '恶露情况': 'lochia_status',
      '婴儿情况': 'baby_condition',
      '疼痛程度': 'pain_level',
      '切口情况': 'wound_status',
      '活动能力': 'mobility',
      '术后用药依从性': 'medication_compliance',
      '症状控制情况': 'symptom_control',
      '慢病用药依从性': 'medication_adherence',
      '生活方式改变': 'lifestyle_changes',
      '下次就诊计划': 'next_visit_plan',
      '疼痛': 'pain',
      '疼痛评分': 'pain_score',
      '睡眠质量': 'sleep_quality',
      '食欲': 'appetite',
      '体重变化': 'weight_change',
      '血压': 'blood_pressure',
      '血糖': 'blood_glucose',
      '心率': 'heart_rate',
      '体温': 'temperature',
      '用药情况': 'medication_status',
      '副作用': 'side_effects',
      '复查时间': 'recheck_time',
      '医嘱执行': 'medical_order_compliance',
      '家属配合': 'family_cooperation',
      '经济状况': 'economic_status',
      '心理状态': 'psychological_status',
      '社会支持': 'social_support',
      '状态': 'status',
      '类型': 'type',
      '级别': 'level',
      '程度': 'degree',
      '情况': 'condition',
      '结果': 'result',
      '时间': 'time',
      '日期': 'date',
      '编号': 'number',
      '代码': 'code',
      '描述': 'description',
      '说明': 'description',
      '详情': 'details',
      '其他': 'other',
      '并发症': 'complications'
    }

    const label = fieldLabel.trim()

    // 直接匹配
    if (medicalTerms[label]) {
      let baseName = medicalTerms[label]

      // 处理重复名称
      const existingNames = existingFields.map(f => f.field_name).filter(Boolean)
      if (existingNames.includes(baseName)) {
        let counter = 2
        while (existingNames.includes(`${baseName}_${counter}`)) {
          counter++
        }
        baseName = `${baseName}_${counter}`
      }

      return baseName
    }

    // 部分匹配
    for (const [term, english] of Object.entries(medicalTerms)) {
      if (label.includes(term)) {
        const prefix = label.replace(term, '').trim()
        if (prefix) {
          // 简单的前缀处理
          const prefixMap = {
            '术前': 'preoperative',
            '术后': 'postoperative',
            '产前': 'prenatal',
            '产后': 'postpartum',
            '左': 'left',
            '右': 'right',
            '上': 'upper',
            '下': 'lower',
            '主要': 'primary',
            '次要': 'secondary',
            '当前': 'current',
            '既往': 'past',
            '家族': 'family'
          }

          const prefixEnglish = prefixMap[prefix] || 'custom'
          return `${prefixEnglish}_${english}`
        }
        return english
      }
    }

    // 如果都没匹配，生成默认名称
    let baseName = label
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fff]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')

    if (!baseName || /^\d/.test(baseName)) {
      baseName = 'custom_field'
    }

    // 处理重复名称
    const existingNames = existingFields.map(f => f.field_name).filter(Boolean)
    if (existingNames.includes(baseName)) {
      let counter = 2
      while (existingNames.includes(`${baseName}_${counter}`)) {
        counter++
      }
      baseName = `${baseName}_${counter}`
    }

    return baseName
  }

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '科室',
      dataIndex: 'department_name',
      key: 'department_name',
    },
    {
      title: '回访方式',
      dataIndex: 'followup_method',
      key: 'followup_method',
      render: (method) => {
        const methodMap = {
          phone: '电话',
          sms: '短信',
          wechat: '微信',
          email: '邮件',
          visit: '上门'
        }
        return methodMap[method] || method
      }
    },
    {
      title: '字段数量',
      key: 'fields_count',
      render: (_, record) => record.fields?.length || 0
    },
    {
      title: '使用次数',
      dataIndex: 'records_count',
      key: 'records_count',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '默认模板',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault) => (
        isDefault ? <Tag color="blue">默认</Tag> : null
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(record)}
          >
            复制
          </Button>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={record.records_count > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleEdit = (template) => {
    setEditingTemplate(template)
    setTemplateFields(template.fields || [])
    setShowAdvancedMode(true) // 编辑时使用高级模式
    setPreviewData(null) // 清空预览数据

    const formData = {
      ...template,
      followup_days: template.followup_schedule?.days || [1, 3, 7],
      followup_time: template.followup_schedule?.time ? dayjs(template.followup_schedule.time, 'HH:mm') : dayjs('09:00', 'HH:mm')
    }

    form.setFieldsValue(formData)
    setIsModalVisible(true)
  }

  const handleCopy = (template) => {
    Modal.confirm({
      title: '复制模板',
      content: (
        <Input
          placeholder="请输入新模板名称"
          defaultValue={`${template.name}_副本`}
          onChange={(e) => {
            Modal.destroyAll()
            copyMutation.mutate({
              templateId: template.id,
              name: e.target.value || `${template.name}_副本`
            })
          }}
        />
      ),
      onOk: () => {},
    })
  }

  const handleSubmit = (values) => {
    // 高级模式下检查字段，简单模式下由后端自动生成
    if (showAdvancedMode && templateFields.length === 0) {
      message.warning('请至少添加一个字段')
      return
    }

    // 非超级管理员自动使用当前用户的科室
    if (currentUser?.role !== 'super_admin' && currentUser?.department_id) {
      values.department_id = currentUser.department_id
    }

    templateMutation.mutate(values)
  }

  const handleAddField = () => {
    setEditingFieldIndex(-1)
    fieldForm.resetFields()
    setIsFieldModalVisible(true)
  }

  const handleEditField = (index) => {
    setEditingFieldIndex(index)
    const field = templateFields[index]

    // 处理字段配置，确保选项数组正确设置
    const formValues = {
      ...field,
      field_config: {
        ...field.field_config,
        options: field.field_config?.options || []
      }
    }

    fieldForm.setFieldsValue(formValues)
    setIsFieldModalVisible(true)
  }

  const handleFieldSubmit = (values) => {
    const newFields = [...templateFields]

    // 处理字段配置
    const fieldData = {
      ...values,
      field_config: values.field_config || {}
    }

    if (editingFieldIndex >= 0) {
      newFields[editingFieldIndex] = { ...fieldData, order: editingFieldIndex }
    } else {
      newFields.push({ ...fieldData, order: newFields.length })
    }

    setTemplateFields(newFields)
    setIsFieldModalVisible(false)
    fieldForm.resetFields()
  }

  const handleDeleteField = (index) => {
    const newFields = templateFields.filter((_, i) => i !== index)
    setTemplateFields(newFields.map((field, i) => ({ ...field, order: i })))
  }

  const fieldTypeOptions = [
    { value: 'text', label: '单行文本' },
    { value: 'textarea', label: '多行文本' },
    { value: 'number', label: '数字' },
    { value: 'select', label: '单选下拉' },
    { value: 'radio', label: '单选按钮' },
    { value: 'checkbox', label: '多选框' },
    { value: 'date', label: '日期' },
  ]

  // 移动端卡片视图渲染函数
  const renderMobileCard = (template) => {
    return (
      <Card
        key={template.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<FileTextOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {template.name}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                ID: {template.id} | {template.fields?.length || 0} 个字段
              </div>
            </div>
          </div>
          <div style={{ display: 'flex', gap: 4 }}>
            {template.is_default && (
              <Tag color="gold" size="small">默认</Tag>
            )}
            <Tag color={template.is_active ? 'green' : 'red'} size="small">
              {template.is_active ? '启用' : '禁用'}
            </Tag>
          </div>
        </div>

        <div className="mobile-card-content">
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>科室</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                <TeamOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                {template.department_name || '全部科室'}
              </div>
            </Col>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>使用次数</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                {template.records_count || 0} 次
              </div>
            </Col>
            <Col span={24}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>回访安排</div>
              <div style={{ fontSize: 14 }}>
                <ClockCircleOutlined style={{ marginRight: 4, color: '#faad14' }} />
                {template.followup_schedule?.days?.join(', ') || '1, 3, 7'} 天后
                ({template.followup_schedule?.time || '09:00'})
              </div>
            </Col>
            {template.description && (
              <Col span={24}>
                <div style={{ fontSize: 12, color: '#8c8c8c' }}>描述</div>
                <div style={{ fontSize: 14 }}>
                  {template.description}
                </div>
              </Col>
            )}
          </Row>
        </div>

        <div className="mobile-card-actions">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(template)}
          >
            编辑
          </Button>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(template)}
          >
            复制
          </Button>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => deleteMutation.mutate(template.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={template.records_count > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 桌面端页面标题 */}
      {!isMobile && (
        <div className="page-header">
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} className="page-title">回访模板</Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingTemplate(null)
                    setTemplateFields([])
                    setPreviewData(null)
                    setShowAdvancedMode(false) // 新增时默认使用简单模式
                    form.resetFields()
                    setIsModalVisible(true)
                  }}
                >
                  新增模板
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      )}

      {/* 桌面端表格 */}
      {!isMobile && (
        <Table
          columns={columns}
          dataSource={templatesData?.templates || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: templatesData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={(newPagination) => setPagination(newPagination)}
        />
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              回访模板 ({templatesData?.total || 0})
            </Title>

            {isLoading ? (
              <Card loading style={{ marginBottom: 12 }} />
            ) : (
              <>
                {templatesData?.templates?.map(renderMobileCard)}

                {/* 移动端分页 */}
                {templatesData?.total > pagination.pageSize && (
                  <Card size="small" style={{ textAlign: 'center', marginTop: 16 }}>
                    <Space>
                      <Button
                        size="small"
                        disabled={pagination.current === 1}
                        onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                      >
                        上一页
                      </Button>
                      <span style={{ fontSize: 12, color: '#8c8c8c' }}>
                        {pagination.current} / {Math.ceil((templatesData?.total || 0) / pagination.pageSize)}
                      </span>
                      <Button
                        size="small"
                        disabled={pagination.current >= Math.ceil((templatesData?.total || 0) / pagination.pageSize)}
                        onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                      >
                        下一页
                      </Button>
                    </Space>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => {
            setEditingTemplate(null)
            setTemplateFields([])
            setPreviewData(null)
            setShowAdvancedMode(false) // 新增时默认使用简单模式
            form.resetFields()
            setIsModalVisible(true)
          }}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 新增/编辑模板弹窗 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '新增模板'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingTemplate(null)
          setTemplateFields([])
          setPreviewData(null)
          setShowAdvancedMode(false)
          form.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 800}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            {/* 只有超级管理员才能选择科室 */}
            {currentUser?.role === 'super_admin' ? (
              <Col span={12}>
                <Form.Item
                  name="department_id"
                  label="所属科室"
                  rules={[{ required: true, message: '请选择所属科室' }]}
                >
                  <Select placeholder="请选择科室">
                    {departmentsData?.departments?.map(dept => (
                      <Option key={dept.id} value={dept.id}>
                        {dept.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            ) : (
              <Col span={12}>
                <Form.Item label="所属科室">
                  <Input
                    value={currentUser?.department_name || '未分配科室'}
                    disabled
                    style={{ backgroundColor: '#f5f5f5' }}
                  />
                  <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                    模版将自动创建在您的科室下
                  </div>
                </Form.Item>
              </Col>
            )}
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} />
          </Form.Item>

          {/* 模式切换 */}
          {!editingTemplate && (
            <Form.Item label="创建模式">
              <Card size="small" style={{ marginBottom: 16 }}>
                <Radio.Group
                  value={showAdvancedMode ? 'advanced' : 'simple'}
                  onChange={(e) => {
                    const isAdvanced = e.target.value === 'advanced'
                    setShowAdvancedMode(isAdvanced)
                    if (!isAdvanced) {
                      // 切换到简单模式时，清空字段并触发预览
                      setTemplateFields([])
                      const templateName = form.getFieldValue('name')
                      if (templateName) {
                        handlePreviewTemplate(templateName)
                      }
                    } else {
                      // 切换到高级模式时，清空预览
                      setPreviewData(null)
                    }
                  }}
                  style={{ width: '100%' }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Radio value="simple">
                      <div>
                        <strong>🎯 匹配模式（推荐）</strong>
                        <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                          根据模板名称自动生成回访字段和配置，如"产科回访"、"术后回访"等
                        </div>
                      </div>
                    </Radio>
                    <Radio value="advanced">
                      <div>
                        <strong>⚙️ 自定义模式</strong>
                        <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                          手动配置回访字段、时间安排和回访方式
                        </div>
                      </div>
                    </Radio>
                  </Space>
                </Radio.Group>
              </Card>
            </Form.Item>
          )}

          {/* 简单模式的预览 */}
          {!showAdvancedMode && !editingTemplate && (
            <Form.Item label="模板预览">
              <Card size="small">
                <Form.Item
                  name="template_name_for_preview"
                  label="输入模板名称查看预览"
                  style={{ marginBottom: 16 }}
                >
                  <Input
                    placeholder="例如：产科回访、术后回访、慢病回访"
                    onChange={(e) => {
                      const value = e.target.value
                      form.setFieldValue('name', value)
                      handlePreviewTemplate(value)
                    }}
                    value={form.getFieldValue('name')}
                  />
                </Form.Item>

                {isPreviewLoading && (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin tip="正在生成预览..." />
                  </div>
                )}

                {previewData && (
                  <div>
                    <Divider orientation="left" style={{ margin: '16px 0 8px 0' }}>
                      <Text strong>🔧 自动配置</Text>
                    </Divider>
                    <Row gutter={16} style={{ marginBottom: 16 }}>
                      <Col span={8}>
                        <Text type="secondary">回访方式：</Text>
                        <Tag color="blue">{
                          previewData.suggested_followup_method === 'phone' ? '电话' :
                          previewData.suggested_followup_method === 'sms' ? '短信' :
                          previewData.suggested_followup_method === 'wechat' ? '微信' :
                          previewData.suggested_followup_method === 'email' ? '邮件' :
                          previewData.suggested_followup_method === 'visit' ? '上门' : '电话'
                        }</Tag>
                      </Col>
                      <Col span={8}>
                        <Text type="secondary">回访时间：</Text>
                        <Tag color="green">{previewData.suggested_followup_schedule?.time || '09:00'}</Tag>
                      </Col>
                      <Col span={8}>
                        <Text type="secondary">回访天数：</Text>
                        <Space size={4}>
                          {(previewData.suggested_followup_schedule?.days || [1, 3, 7]).map(day => (
                            <Tag key={day} color="orange">{day}天</Tag>
                          ))}
                        </Space>
                      </Col>
                    </Row>

                    <Divider orientation="left" style={{ margin: '16px 0 8px 0' }}>
                      <Text strong>📋 自动生成字段 ({previewData.total_fields}个)</Text>
                    </Divider>
                    <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                      {previewData.fields?.map((field, index) => (
                        <div
                          key={index}
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '6px 12px',
                            backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                            borderRadius: '4px',
                            marginBottom: '4px'
                          }}
                        >
                          <div>
                            <Text strong>{field.field_label}</Text>
                            <Text type="secondary" style={{ marginLeft: 8 }}>
                              ({fieldTypeOptions.find(opt => opt.value === field.field_type)?.label})
                            </Text>
                          </div>
                          {field.is_required && (
                            <Tag color="red" size="small">必填</Tag>
                          )}
                        </div>
                      ))}
                    </div>

                    <Alert
                      message="💡 提示"
                      description="系统将根据模板名称自动生成合适的回访字段和配置。创建后您仍可以编辑和调整。"
                      type="info"
                      showIcon
                      style={{ marginTop: 16 }}
                    />
                  </div>
                )}

                {!previewData && !isPreviewLoading && form.getFieldValue('name') && (
                  <Alert
                    message="未识别的模板类型"
                    description="系统将生成基础回访字段，您可以创建后再进行编辑，或切换到自定义模式手动配置。"
                    type="warning"
                    showIcon
                  />
                )}
              </Card>
            </Form.Item>
          )}

          {/* 高级模式下的回访配置 */}
          {showAdvancedMode && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="followup_method"
                  label="回访方式"
                  rules={[{ required: true, message: '请选择回访方式' }]}
                >
                  <Select>
                    <Option value="phone">电话</Option>
                    <Option value="sms">短信</Option>
                    <Option value="wechat">微信</Option>
                    <Option value="email">邮件</Option>
                    <Option value="visit">上门</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="followup_time"
                  label="回访时间"
                  initialValue={dayjs('09:00', 'HH:mm')}
                >
                  <TimePicker format="HH:mm" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="followup_days"
                  label="回访天数"
                  initialValue={[1, 3, 7]}
                >
                  <Select mode="tags" placeholder="输入回访天数">
                    <Option value={1}>1天</Option>
                    <Option value={3}>3天</Option>
                    <Option value={7}>7天</Option>
                    <Option value={30}>30天</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_default"
                label="默认模板"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>

          {/* 高级模式下的字段配置 */}
          {showAdvancedMode && (
            <Form.Item label="回访字段">
              <Card
                title="字段列表"
                extra={
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={handleAddField}
                  >
                    添加字段
                  </Button>
                }
                size="small"
              >
                {templateFields.length === 0 ? (
                  <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                    暂无字段，请添加字段
                  </div>
                ) : (
                  <div>
                    {templateFields.map((field, index) => (
                      <div
                        key={index}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '8px 0',
                          borderBottom: '1px solid #f0f0f0'
                        }}
                      >
                        <div>
                          <strong>{field.field_label}</strong>
                          <span style={{ marginLeft: 8, color: '#666' }}>
                            ({fieldTypeOptions.find(opt => opt.value === field.field_type)?.label})
                          </span>
                          {field.is_required && (
                            <Tag color="red" size="small" style={{ marginLeft: 8 }}>必填</Tag>
                          )}
                        </div>
                        <Space>
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleEditField(index)}
                          >
                            编辑
                          </Button>
                          <Button
                            type="link"
                            size="small"
                            danger
                            onClick={() => handleDeleteField(index)}
                          >
                            删除
                          </Button>
                        </Space>
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={templateMutation.isLoading}
              >
                {editingTemplate ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false)
                  setEditingTemplate(null)
                  setTemplateFields([])
                  setPreviewData(null)
                  setShowAdvancedMode(false)
                  form.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 字段编辑弹窗 */}
      <Modal
        title={editingFieldIndex >= 0 ? '编辑字段' : '添加字段'}
        open={isFieldModalVisible}
        onCancel={() => {
          setIsFieldModalVisible(false)
          fieldForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={fieldForm}
          layout="vertical"
          onFinish={handleFieldSubmit}
        >
          {/* 隐藏的字段名称，自动生成 */}
          <Form.Item name="field_name" style={{ display: 'none' }}>
            <Input />
          </Form.Item>

          <Form.Item
            name="field_label"
            label="字段标签"
            rules={[{ required: true, message: '请输入字段标签' }]}
          >
            <Input
              placeholder="如: 满意度"
              onChange={(e) => {
                const label = e.target.value
                if (label) {
                  // 自动生成字段名称
                  const fieldName = generateFieldName(label, templateFields)
                  fieldForm.setFieldsValue({ field_name: fieldName })
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="field_type"
            label="字段类型"
            rules={[{ required: true, message: '请选择字段类型' }]}
          >
            <Select>
              {fieldTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 选项配置 - 仅对 select, radio, checkbox 类型显示 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.field_type !== currentValues.field_type
            }
          >
            {({ getFieldValue }) => {
              const fieldType = getFieldValue('field_type')
              if (['select', 'radio', 'checkbox'].includes(fieldType)) {
                return (
                  <Form.Item
                    name={['field_config', 'options']}
                    label="选项配置"
                    rules={[{ required: true, message: '请至少添加一个选项' }]}
                  >
                    <Form.List name={['field_config', 'options']}>
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                              <Form.Item
                                {...restField}
                                name={name}
                                rules={[{ required: true, message: '请输入选项内容' }]}
                              >
                                <Input placeholder="选项内容" />
                              </Form.Item>
                              <MinusCircleOutlined onClick={() => remove(name)} />
                            </Space>
                          ))}
                          <Form.Item>
                            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                              添加选项
                            </Button>
                          </Form.Item>
                        </>
                      )}
                    </Form.List>
                  </Form.Item>
                )
              }
              return null
            }}
          </Form.Item>

          <Form.Item
            name="is_required"
            label="是否必填"
            valuePropName="checked"
            initialValue={false}
          >
            <Checkbox>必填字段</Checkbox>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingFieldIndex >= 0 ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setIsFieldModalVisible(false)
                  fieldForm.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Templates
