import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Radio,
  Checkbox,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Card,
  Tag,
  FloatButton,
  List,
  Avatar
} from 'antd'
import HistoryInput from '../components/HistoryInput'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  PhoneOutlined,
  HomeOutlined,
  MedicineBoxOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import api from '../services/api'
import { departmentService } from '../services/departments'
import { userService } from '../services/users'
import { authService } from '../services/auth'

const { Title } = Typography
const { TextArea } = Input
const { Option } = Select
const { Search } = Input

const Patients = () => {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingPatient, setEditingPatient] = useState(null)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })
  const [searchText, setSearchText] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [dynamicFields, setDynamicFields] = useState({})
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      const mobile = window.innerWidth <= 768
      console.log('屏幕宽度:', window.innerWidth, '是否移动端:', mobile)
      setIsMobile(mobile)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 获取患者列表
  const { data: patientsData, isLoading, refetch } = useQuery(
    ['patients', pagination.current, pagination.pageSize, searchText],
    () => api.get('/patients', {
      params: {
        page: pagination.current,
        per_page: pagination.pageSize,
        search: searchText
      }
    }),
    {
      keepPreviousData: true,
      onSuccess: (data) => {
        console.log('患者数据获取成功:', data)
      },
      onError: (error) => {
        console.error('患者数据获取失败:', error)
        message.error('获取患者数据失败')
      }
    }
  )

  // 获取当前用户信息
  const { data: currentUser } = useQuery('currentUser', authService.getCurrentUser)

  // 获取科室列表
  const { data: departmentsData } = useQuery(
    'departments',
    async () => {
      const response = await departmentService.getDepartments({ per_page: 100 })
      return response
    }
  )

  // 获取医生列表
  const { data: doctorsData } = useQuery(
    'doctors',
    () => userService.getUsers({ per_page: 100, role: 'user' })
  )

  // 获取患者信息模板列表
  const { data: templatesData } = useQuery(
    'patientTemplates',
    async () => {
      const response = await api.get('/patient-templates', {
        params: { per_page: 100 }
      })
      console.log('患者信息模板数据:', response)
      return response
    }
  )

  // 创建/更新患者
  const patientMutation = useMutation(
    (patientData) => {
      const data = { ...patientData }
      
      // 处理日期格式
      if (data.admission_date) {
        data.admission_date = data.admission_date.format('YYYY-MM-DD')
      }
      if (data.discharge_date) {
        data.discharge_date = data.discharge_date.format('YYYY-MM-DD')
      }

      // 添加动态字段数据
      data.data = dynamicFields
      data.template_id = selectedTemplate?.id

      if (editingPatient) {
        return api.put(`/patients/${editingPatient.id}`, data)
      } else {
        return api.post('/patients', data)
      }
    },
    {
      onSuccess: () => {
        message.success(editingPatient ? '患者更新成功' : '患者创建成功')
        setIsModalVisible(false)
        setEditingPatient(null)
        setSelectedTemplate(null)
        setDynamicFields({})
        form.resetFields()
        queryClient.invalidateQueries('patients')
      },
      onError: (error) => {
        message.error(error.message || '操作失败')
      }
    }
  )

  // 删除患者
  const deleteMutation = useMutation(
    (patientId) => api.delete(`/patients/${patientId}`),
    {
      onSuccess: () => {
        message.success('患者删除成功')
        queryClient.invalidateQueries('patients')
      },
      onError: (error) => {
        message.error(error.message || '删除失败')
      }
    }
  )

  const handleTableChange = (newPagination) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    })
  }

  const handleSearch = (value) => {
    setSearchText(value)
    setPagination({ ...pagination, current: 1 })
  }

  const handleEdit = (record) => {
    setEditingPatient(record)
    setSelectedTemplate(record.template)
    setDynamicFields(record.data || {})

    // 填充表单
    const formData = { ...record.data }

    // 动态处理所有日期和日期时间字段
    if (record.template?.fields) {
      record.template.fields.forEach(field => {
        if ((field.field_type === 'date' || field.field_type === 'datetime') && record.data?.[field.field_name]) {
          try {
            formData[field.field_name] = dayjs(record.data[field.field_name])
          } catch (error) {
            console.warn(`日期字段 ${field.field_name} 格式错误:`, record.data[field.field_name])
          }
        }
      })
    }

    form.setFieldsValue(formData)
    setIsModalVisible(true)
  }

  const handleCreate = () => {
    setEditingPatient(null)
    setSelectedTemplate(null)
    setDynamicFields({})
    form.resetFields()

    // 查找当前用户科室的默认模板并自动选择
    let defaultTemplate = null
    if (currentUser?.department_id) {
      // 优先选择当前用户科室的默认模板
      defaultTemplate = templatesData?.templates?.find(t =>
        t.is_default && t.department_id === currentUser.department_id
      )
    }

    // 如果没有找到科室默认模板，则选择任意默认模板
    if (!defaultTemplate) {
      defaultTemplate = templatesData?.templates?.find(t => t.is_default)
    }

    if (defaultTemplate) {
      setSelectedTemplate(defaultTemplate)
      form.setFieldsValue({ template_id: defaultTemplate.id })

      // 初始化默认模板的字段
      const initialFields = {}
      if (defaultTemplate.fields) {
        defaultTemplate.fields.forEach(field => {
          initialFields[field.field_name] = field.default_value || ''
        })
      }
      setDynamicFields(initialFields)
      form.setFieldsValue(initialFields)

      console.log('自动选择默认模板:', defaultTemplate.name, '(科室:', defaultTemplate.department_name, ')')
    }

    setIsModalVisible(true)
  }

  const handleTemplateChange = (templateId) => {
    const template = templatesData?.templates?.find(t => t.id === templateId)
    setSelectedTemplate(template)

    console.log('选择的模板:', template)

    // 重置动态字段
    const initialFields = {}
    if (template?.fields) {
      template.fields.forEach(field => {
        initialFields[field.field_name] = field.default_value || ''
      })
    }
    setDynamicFields(initialFields)
    form.setFieldsValue(initialFields)
  }

  const handleFormSubmit = () => {
    form.validateFields().then(async (values) => {
      // 处理日期时间字段格式化
      const processedValues = { ...values }
      if (selectedTemplate?.fields) {
        selectedTemplate.fields.forEach(field => {
          if (field.field_type === 'date' && processedValues[field.field_name]) {
            // 日期字段格式化为 YYYY-MM-DD
            processedValues[field.field_name] = processedValues[field.field_name].format('YYYY-MM-DD')
          } else if (field.field_type === 'datetime' && processedValues[field.field_name]) {
            // 日期时间字段格式化为 ISO 字符串
            processedValues[field.field_name] = processedValues[field.field_name].toISOString()
          }
        })
      }

      // 更新动态字段
      setDynamicFields(processedValues)

      // 批量记录字段历史
      if (selectedTemplate) {
        try {
          const fields = []
          selectedTemplate.fields?.forEach(field => {
            const fieldValue = processedValues[field.field_name]
            if (fieldValue && (field.field_type === 'text' || field.field_type === 'textarea')) {
              fields.push({
                field_name: field.field_name,
                field_value: fieldValue
              })
            }
          })

          if (fields.length > 0) {
            await api.post('/field-history/batch-record', {
              fields: fields,
              template_type: 'patient',
              template_id: selectedTemplate.id
            })
          }
        } catch (error) {
          console.error('记录字段历史失败:', error)
          // 不影响主要流程，只记录错误
        }
      }

      patientMutation.mutate(processedValues)
    })
  }

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '模板',
      dataIndex: ['template', 'name'],
      key: 'template_name',
      width: 120,
    },
    {
      title: '患者信息',
      key: 'patient_info',
      width: 200,
      render: (_, record) => {
        const data = record.data || {}
        return (
          <div>
            <div><strong>姓名:</strong> {data.name || '-'}</div>
            <div><strong>性别:</strong> {data.gender || '-'}</div>
            <div><strong>年龄:</strong> {data.age || '-'}</div>
          </div>
        )
      }
    },
    {
      title: '联系方式',
      key: 'contact',
      width: 150,
      render: (_, record) => {
        const data = record.data || {}
        return (
          <div>
            <div><strong>电话:</strong> {data.phone || '-'}</div>
            <div><strong>地址:</strong> {data.address || '-'}</div>
          </div>
        )
      }
    },
    {
      title: '医疗信息',
      key: 'medical',
      width: 200,
      render: (_, record) => {
        const data = record.data || {}
        return (
          <div>
            <div><strong>病历号:</strong> {data.medical_record_no || '-'}</div>
            <div><strong>诊断:</strong> {data.diagnosis || '-'}</div>
            <div><strong>科室:</strong> {data.department_name || '-'}</div>
          </div>
        )
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个患者吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 移动端卡片视图渲染函数
  const renderMobileCard = (patient) => {
    const data = patient.data || {}

    return (
      <Card
        key={patient.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {data.name || data.patient_name || '未知姓名'}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                ID: {patient.id} | {patient.template?.name || '无模板'}
              </div>
            </div>
          </div>
          <Tag color="blue" size="small">
            {data.gender || '未知'}
          </Tag>
        </div>

        <div className="mobile-card-content">
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>年龄</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                {data.age || data.patient_age || '-'}
              </div>
            </Col>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>电话</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                <PhoneOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                {data.phone || data.patient_phone || '-'}
              </div>
            </Col>
            <Col span={24}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>地址</div>
              <div style={{ fontSize: 14 }}>
                <HomeOutlined style={{ marginRight: 4, color: '#faad14' }} />
                {data.address || '-'}
              </div>
            </Col>
            <Col span={24}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>诊断</div>
              <div style={{ fontSize: 14 }}>
                <MedicineBoxOutlined style={{ marginRight: 4, color: '#ff4d4f' }} />
                {data.diagnosis || '-'}
              </div>
            </Col>
          </Row>
        </div>

        <div className="mobile-card-actions">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(patient)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个患者吗？"
            onConfirm={() => deleteMutation.mutate(patient.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      </Card>
    )
  }

  console.log('患者页面渲染 - isMobile:', isMobile, '屏幕宽度:', window.innerWidth)

  return (
    <div>
      {/* 调试信息 */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        background: 'red',
        color: 'white',
        padding: '4px 8px',
        fontSize: '12px',
        zIndex: 9999
      }}>
        {isMobile ? '移动端' : '桌面端'} ({window.innerWidth}px)
      </div>

      {/* 移动端搜索栏 */}
      {isMobile && (
        <div className="mobile-search-bar">
          <Search
            placeholder="搜索患者"
            allowClear
            onSearch={handleSearch}
            style={{ marginBottom: 8 }}
          />
        </div>
      )}

      {/* 页面标题 - 桌面端 */}
      {!isMobile && (
        <Card>
          <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
            <Col>
              <Title level={4} style={{ margin: 0 }}>患者管理</Title>
            </Col>
            <Col>
              <Space>
                <Search
                  placeholder="搜索患者"
                  allowClear
                  onSearch={handleSearch}
                  style={{ width: 200 }}
                />
                <Button icon={<ReloadOutlined />} onClick={refetch}>
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新增患者
                </Button>
              </Space>
            </Col>
          </Row>

          <Table
            columns={columns}
            dataSource={patientsData?.patients || []}
            rowKey="id"
            loading={isLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: patientsData?.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
          />
        </Card>
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              患者管理 ({patientsData?.total || 0})
            </Title>

            {isLoading ? (
              <Card loading style={{ marginBottom: 12 }} />
            ) : (
              <>
                {patientsData?.patients?.map(renderMobileCard)}

                {/* 移动端分页 */}
                {patientsData?.total > pagination.pageSize && (
                  <Card size="small" style={{ textAlign: 'center', marginTop: 16 }}>
                    <Space>
                      <Button
                        size="small"
                        disabled={pagination.current === 1}
                        onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                      >
                        上一页
                      </Button>
                      <span style={{ fontSize: 12, color: '#8c8c8c' }}>
                        {pagination.current} / {Math.ceil((patientsData?.total || 0) / pagination.pageSize)}
                      </span>
                      <Button
                        size="small"
                        disabled={pagination.current >= Math.ceil((patientsData?.total || 0) / pagination.pageSize)}
                        onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                      >
                        下一页
                      </Button>
                    </Space>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={handleCreate}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 新增/编辑患者弹窗 */}
      <Modal
        title={editingPatient ? '编辑患者' : '新增患者'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingPatient(null)
          setSelectedTemplate(null)
          setDynamicFields({})
          form.resetFields()
        }}
        onOk={handleFormSubmit}
        confirmLoading={patientMutation.isLoading}
        width={isMobile ? '100%' : 800}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(changedValues, allValues) => {
            setDynamicFields(allValues)
          }}
        >
          {!editingPatient && (
            <Form.Item
              label="选择患者信息模板"
              name="template_id"
              rules={[{ required: true, message: '请选择患者信息模板' }]}
            >
              <Select
                placeholder="请选择患者信息模板"
                onChange={handleTemplateChange}
                loading={!templatesData}
              >
                {templatesData?.templates?.map(template => (
                  <Option key={template.id} value={template.id}>
                    {template.name} ({template.department_name})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}

          {selectedTemplate && (
            <div>
              <Title level={5}>患者信息</Title>
              <Row gutter={isMobile ? [0, 16] : 16}>
                {selectedTemplate.fields?.map(field => (
                  <Col
                    span={isMobile ? 24 : (field.field_type === 'textarea' ? 24 : 12)}
                    key={field.field_name}
                  >
                    <Form.Item
                      label={field.field_label}
                      name={field.field_name}
                      rules={field.is_required ? [{ required: true, message: `请输入${field.field_label}` }] : []}
                      className={isMobile ? 'mobile-form-item' : ''}
                    >
                      {field.field_type === 'text' && (
                        <HistoryInput
                          fieldName={field.field_name}
                          templateType="patient"
                          templateId={selectedTemplate?.id}
                          type="text"
                          placeholder={`请输入${field.field_label}`}
                          style={isMobile ? { height: 44, fontSize: 16 } : {}}
                        />
                      )}
                      {field.field_type === 'number' && (
                        <Input
                          type="number"
                          placeholder={`请输入${field.field_label}`}
                          style={isMobile ? { height: 44, fontSize: 16 } : {}}
                        />
                      )}
                      {field.field_type === 'textarea' && (
                        <HistoryInput
                          fieldName={field.field_name}
                          templateType="patient"
                          templateId={selectedTemplate?.id}
                          type="textarea"
                          rows={3}
                          placeholder={`请输入${field.field_label}`}
                          style={isMobile ? { fontSize: 16 } : {}}
                        />
                      )}
                      {field.field_type === 'select' && (
                        <Select
                          placeholder={`请选择${field.field_label}`}
                          style={isMobile ? { fontSize: 16 } : {}}
                        >
                          {field.field_config?.options?.map(option => {
                            // 支持字符串数组和对象数组两种格式
                            const value = typeof option === 'string' ? option : option.value
                            const label = typeof option === 'string' ? option : option.label
                            return (
                              <Option key={value} value={value}>{label}</Option>
                            )
                          })}
                        </Select>
                      )}
                      {field.field_type === 'radio' && (
                        <Radio.Group>
                          {field.field_config?.options?.map(option => {
                            const value = typeof option === 'string' ? option : option.value
                            const label = typeof option === 'string' ? option : option.label
                            return (
                              <Radio key={value} value={value}>{label}</Radio>
                            )
                          })}
                        </Radio.Group>
                      )}
                      {field.field_type === 'checkbox' && (
                        <Checkbox.Group>
                          {field.field_config?.options?.map(option => {
                            const value = typeof option === 'string' ? option : option.value
                            const label = typeof option === 'string' ? option : option.label
                            return (
                              <Checkbox key={value} value={value}>{label}</Checkbox>
                            )
                          })}
                        </Checkbox.Group>
                      )}
                      {field.field_type === 'date' && (
                        <DatePicker
                          style={{
                            width: '100%',
                            ...(isMobile ? { height: 44, fontSize: 16 } : {})
                          }}
                          placeholder={`请选择${field.field_label}`}
                        />
                      )}
                      {field.field_type === 'datetime' && (
                        <DatePicker
                          showTime
                          style={{
                            width: '100%',
                            ...(isMobile ? { height: 44, fontSize: 16 } : {})
                          }}
                          placeholder={`请选择${field.field_label}`}
                        />
                      )}
                    </Form.Item>
                  </Col>
                ))}
              </Row>
            </div>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default Patients
