import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Tag,
  Card,
  Row,
  Col,
  Checkbox,
  Typography,
  Divider,
  Spin,
  Popconfirm,
  Avatar,
  FloatButton
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  SettingOutlined,
  MinusCircleOutlined,
  FileTextOutlined,
  TeamOutlined
} from '@ant-design/icons'
import api from '../services/api'
import { departmentService } from '../services/departments'
import { authService } from '../services/auth'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

const PatientTemplates = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isFieldModalVisible, setIsFieldModalVisible] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [editingFieldIndex, setEditingFieldIndex] = useState(-1)
  const [templateFields, setTemplateFields] = useState([])
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })
  const [previewFields, setPreviewFields] = useState([])
  const [isPreviewLoading, setIsPreviewLoading] = useState(false)
  const [showAdvancedMode, setShowAdvancedMode] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  const queryClient = useQueryClient()
  const [form] = Form.useForm()
  const [fieldForm] = Form.useForm()

  // 字段类型选项
  const fieldTypeOptions = [
    { value: 'text', label: '单行文本' },
    { value: 'textarea', label: '多行文本' },
    { value: 'number', label: '数字' },
    { value: 'select', label: '下拉选择' },
    { value: 'radio', label: '单选按钮' },
    { value: 'checkbox', label: '复选框' },
    { value: 'date', label: '日期' },
    { value: 'datetime', label: '日期时间' }
  ]

  // 获取患者信息模板列表
  const { data: templatesData, isLoading, error, refetch } = useQuery(
    ['patientTemplates', pagination.current, pagination.pageSize],
    async () => {
      try {
        const response = await api.get('/patient-templates', {
          params: {
            page: pagination.current,
            per_page: pagination.pageSize
          }
        })

        // 后端返回的是 templates 字段，需要转换为 data 字段以匹配前端期望
        return {
          data: response?.templates || [],
          total: response?.total || 0,
          pages: response?.pages || 0,
          current_page: response?.current_page || 1
        }
      } catch (error) {
        console.error('获取患者信息模板列表失败:', error)
        throw error
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟内数据保持新鲜
      cacheTime: 10 * 60 * 1000  // 10分钟缓存时间
    }
  )

  // 错误处理
  React.useEffect(() => {
    if (error) {
      console.error('患者信息模板数据加载错误:', error)
    }
  }, [error])

  // 获取当前用户信息
  const { data: currentUser } = useQuery('currentUser', authService.getCurrentUser)

  // 获取科室列表（仅超级管理员需要）
  const { data: departmentsData, isLoading: departmentsLoading, error: departmentsError } = useQuery(
    'departments',
    async () => {
      const response = await departmentService.getDepartments({ per_page: 100 })
      return response
    },
    {
      enabled: currentUser?.role === 'super_admin' // 只有超级管理员才获取科室列表
    }
  )

  // 创建模板
  const createMutation = useMutation(
    async (templateData) => {
      const response = await api.post('/patient-templates', templateData)
      return response.data
    },
    {
      onSuccess: (data) => {
        message.success('患者信息模板创建成功')
        setIsModalVisible(false)
        form.resetFields()
        setTemplateFields([])
        // 使用 queryClient 强制刷新所有相关查询
        queryClient.invalidateQueries(['patientTemplates'])
        refetch()
      },
      onError: (error) => {
        console.error('创建患者信息模板失败:', error)
        message.error(error.message || '创建失败')
      }
    }
  )

  // 更新模板
  const updateMutation = useMutation(
    async ({ id, ...templateData }) => {
      const response = await api.put(`/patient-templates/${id}`, templateData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('患者信息模板更新成功')
        setIsModalVisible(false)
        form.resetFields()
        setTemplateFields([])
        setEditingTemplate(null)
        // 使用 queryClient 强制刷新所有相关查询
        queryClient.invalidateQueries(['patientTemplates'])
        refetch()
      },
      onError: (error) => {
        message.error(error.response?.data?.error || '更新失败')
      }
    }
  )

  // 删除模板
  const deleteMutation = useMutation(
    async (templateId) => {
      const response = await api.delete(`/patient-templates/${templateId}`)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('患者信息模板删除成功')
        // 使用 queryClient 强制刷新所有相关查询
        queryClient.invalidateQueries(['patientTemplates'])
        refetch()
      },
      onError: (error) => {
        message.error(error.response?.data?.error || '删除失败')
      }
    }
  )

  // 复制模板
  const copyMutation = useMutation(
    async ({ templateId, name }) => {
      const response = await api.post(`/patient-templates/${templateId}/copy`, { name })
      return response.data
    },
    {
      onSuccess: () => {
        message.success('患者信息模板复制成功')
        refetch()
      },
      onError: (error) => {
        message.error(error.response?.data?.error || '复制失败')
      }
    }
  )

  const handleCreate = () => {
    setEditingTemplate(null)
    setTemplateFields([])
    setPreviewFields([])
    setShowAdvancedMode(false)
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEdit = (template) => {
    setEditingTemplate(template)
    setTemplateFields(template.fields || [])
    setPreviewFields([])
    setShowAdvancedMode(true) // 编辑时使用高级模式
    form.setFieldsValue({
      name: template.name,
      description: template.description,
      department_id: template.department_id,
      is_active: template.is_active,
      is_default: template.is_default
    })
    setIsModalVisible(true)
  }

  const handleCopy = (template) => {
    Modal.confirm({
      title: '复制患者信息模板',
      content: (
        <Input
          placeholder="请输入新模板名称"
          defaultValue={`${template.name}_副本`}
          onChange={(e) => {
            Modal.destroyAll()
            copyMutation.mutate({
              templateId: template.id,
              name: e.target.value || `${template.name}_副本`
            })
          }}
        />
      ),
      onOk: () => {}
    })
  }

  const handleSubmit = (values) => {
    const templateData = {
      ...values,
      fields: showAdvancedMode ? templateFields : undefined // 简单模式下不传字段，让后端自动生成
    }

    // 非超级管理员自动使用当前用户的科室
    if (currentUser?.role !== 'super_admin' && currentUser?.department_id) {
      templateData.department_id = currentUser.department_id
    }

    if (editingTemplate) {
      updateMutation.mutate({ id: editingTemplate.id, ...templateData })
    } else {
      createMutation.mutate(templateData)
    }
  }

  // 预览字段
  const handlePreviewFields = async (templateName) => {
    if (!templateName) {
      setPreviewFields([])
      return
    }

    setIsPreviewLoading(true)
    try {
      const response = await api.post('/patient-templates/preview-fields', {
        name: templateName
      })
      setPreviewFields(response.fields || [])
    } catch (error) {
      console.error('预览字段失败:', error)
      message.error('预览字段失败')
      setPreviewFields([])
    } finally {
      setIsPreviewLoading(false)
    }
  }

  // 生成字段名称
  const generateFieldName = (fieldLabel, existingFields = []) => {
    if (!fieldLabel) return ''

    // 常用医疗术语中英文对照表
    const medicalTerms = {
      '姓名': 'name',
      '性别': 'gender',
      '年龄': 'age',
      '出生日期': 'birth_date',
      '身份证号': 'id_number',
      '联系电话': 'phone',
      '手机号': 'mobile',
      '电话': 'phone',
      '地址': 'address',
      '住址': 'address',
      '家庭住址': 'home_address',
      '邮编': 'postal_code',
      '邮箱': 'email',
      '职业': 'occupation',
      '婚姻状况': 'marital_status',
      '紧急联系人': 'emergency_contact',
      '紧急联系电话': 'emergency_phone',
      '病历号': 'medical_record_number',
      '住院号': 'admission_number',
      '床号': 'bed_number',
      '科室': 'department',
      '医生': 'doctor',
      '主治医生': 'attending_doctor',
      '入院日期': 'admission_date',
      '出院日期': 'discharge_date',
      '住院天数': 'hospital_days',
      '诊断': 'diagnosis',
      '主要诊断': 'primary_diagnosis',
      '次要诊断': 'secondary_diagnosis',
      '病史': 'medical_history',
      '过敏史': 'allergy_history',
      '家族史': 'family_history',
      '现病史': 'present_illness',
      '既往史': 'past_history',
      '孕周': 'gestational_age',
      '胎次': 'gravida',
      '产次': 'para',
      '分娩方式': 'delivery_method',
      '新生儿体重': 'birth_weight',
      '新生儿身长': 'birth_length',
      '新生儿性别': 'baby_gender',
      'Apgar评分': 'apgar_score',
      '胎盘重量': 'placenta_weight',
      '羊水': 'amniotic_fluid',
      '脐带': 'umbilical_cord',
      '会阴': 'perineum',
      '产后出血': 'postpartum_hemorrhage',
      '母乳喂养': 'breastfeeding',
      '恶露': 'lochia',
      '子宫复旧': 'uterine_involution',
      '出生体重': 'birth_weight',
      '当前体重': 'current_weight',
      '当前身高': 'current_height',
      '头围': 'head_circumference',
      '胸围': 'chest_circumference',
      '喂养方式': 'feeding_method',
      '疫苗接种': 'vaccination',
      '疫苗接种情况': 'vaccination_status',
      '发育情况': 'development_status',
      '营养状况': 'nutritional_status',
      '手术日期': 'surgery_date',
      '手术类型': 'surgery_type',
      '手术名称': 'surgery_name',
      '麻醉方式': 'anesthesia_method',
      '手术时长': 'surgery_duration',
      '术前诊断': 'preoperative_diagnosis',
      '术后诊断': 'postoperative_diagnosis',
      '手术医生': 'surgeon',
      '麻醉医生': 'anesthesiologist',
      '切口': 'incision',
      '切口愈合': 'wound_healing',
      '术后并发症': 'postoperative_complications',
      '引流': 'drainage',
      '主诉': 'chief_complaint',
      '体格检查': 'physical_examination',
      '辅助检查': 'auxiliary_examination',
      '实验室检查': 'laboratory_test',
      '影像学检查': 'imaging_examination',
      '心电图': 'ecg',
      '血压': 'blood_pressure',
      '心率': 'heart_rate',
      '体温': 'temperature',
      '呼吸': 'respiration',
      '血糖': 'blood_glucose',
      '血脂': 'blood_lipid',
      '用药': 'medication',
      '药物': 'medicine',
      '出院用药': 'discharge_medication',
      '用药史': 'medication_history',
      '用药依从性': 'medication_compliance',
      '药物过敏': 'drug_allergy',
      '剂量': 'dosage',
      '用法': 'usage',
      '频次': 'frequency',
      '联系状态': 'contact_status',
      '回访时间': 'followup_time',
      '回访方式': 'followup_method',
      '回访结果': 'followup_result',
      '总体情况': 'overall_condition',
      '满意度': 'satisfaction',
      '建议': 'suggestion',
      '备注': 'notes',
      '下次回访': 'next_followup',
      '症状改善': 'symptom_improvement',
      '康复情况': 'recovery_status',
      '就诊日期': 'visit_date',
      '分诊级别': 'triage_level',
      '处理结果': 'treatment_result',
      '转归': 'outcome',
      '状态': 'status',
      '类型': 'type',
      '级别': 'level',
      '程度': 'degree',
      '情况': 'condition',
      '结果': 'result',
      '时间': 'time',
      '日期': 'date',
      '编号': 'number',
      '代码': 'code',
      '描述': 'description',
      '说明': 'description',
      '详情': 'details',
      '其他': 'other',
      '并发症': 'complications'
    }

    const label = fieldLabel.trim()

    // 直接匹配
    if (medicalTerms[label]) {
      let baseName = medicalTerms[label]

      // 处理重复名称
      const existingNames = existingFields.map(f => f.field_name).filter(Boolean)
      if (existingNames.includes(baseName)) {
        let counter = 2
        while (existingNames.includes(`${baseName}_${counter}`)) {
          counter++
        }
        baseName = `${baseName}_${counter}`
      }

      return baseName
    }

    // 处理包含单位的字段
    const unitMappings = {
      '(g)': '_g',
      '(kg)': '_kg',
      '(cm)': '_cm',
      '(mm)': '_mm',
      '(ml)': '_ml',
      '(分钟)': '_minutes',
      '(小时)': '_hours',
      '(天)': '_days',
      '(周)': '_weeks',
      '(月)': '_months',
      '(年)': '_years',
      '(次)': '_times',
      '(度)': '_degree',
      '(%)': '_percent',
    }

    for (const [unit, suffix] of Object.entries(unitMappings)) {
      if (label.includes(unit)) {
        const baseLabel = label.replace(unit, '').trim()
        if (medicalTerms[baseLabel]) {
          return medicalTerms[baseLabel] + suffix
        }
      }
    }

    // 部分匹配
    for (const [term, english] of Object.entries(medicalTerms)) {
      if (label.includes(term)) {
        const prefix = label.replace(term, '').trim()
        if (prefix) {
          // 简单的前缀处理
          const prefixMap = {
            '术前': 'preoperative',
            '术后': 'postoperative',
            '产前': 'prenatal',
            '产后': 'postpartum',
            '左': 'left',
            '右': 'right',
            '上': 'upper',
            '下': 'lower',
            '主要': 'primary',
            '次要': 'secondary',
            '当前': 'current',
            '既往': 'past',
            '家族': 'family'
          }

          const prefixEnglish = prefixMap[prefix] || 'custom'
          return `${prefixEnglish}_${english}`
        }
        return english
      }
    }

    // 如果都没匹配，生成默认名称
    let baseName = label
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fff]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')

    if (!baseName || /^\d/.test(baseName)) {
      baseName = 'custom_field'
    }

    // 处理重复名称
    const existingNames = existingFields.map(f => f.field_name).filter(Boolean)
    if (existingNames.includes(baseName)) {
      let counter = 2
      while (existingNames.includes(`${baseName}_${counter}`)) {
        counter++
      }
      baseName = `${baseName}_${counter}`
    }

    return baseName
  }

  const handleAddField = () => {
    setEditingFieldIndex(-1)
    fieldForm.resetFields()
    setIsFieldModalVisible(true)
  }

  const handleEditField = (index) => {
    setEditingFieldIndex(index)
    const field = templateFields[index]
    
    // 处理字段配置，确保选项数组正确设置
    const formValues = {
      ...field,
      field_config: {
        ...field.field_config,
        options: field.field_config?.options || []
      }
    }
    
    fieldForm.setFieldsValue(formValues)
    setIsFieldModalVisible(true)
  }

  const handleFieldSubmit = (values) => {
    const newFields = [...templateFields]
    
    // 处理字段配置
    const fieldData = {
      ...values,
      field_config: values.field_config || {}
    }
    
    if (editingFieldIndex >= 0) {
      newFields[editingFieldIndex] = { ...fieldData, order: editingFieldIndex }
    } else {
      newFields.push({ ...fieldData, order: newFields.length })
    }
    
    setTemplateFields(newFields)
    setIsFieldModalVisible(false)
    fieldForm.resetFields()
  }

  const handleDeleteField = (index) => {
    const newFields = templateFields.filter((_, i) => i !== index)
    setTemplateFields(newFields.map((field, i) => ({ ...field, order: i })))
  }

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          {record.is_default && <Tag color="blue" size="small">默认</Tag>}
        </div>
      )
    },
    {
      title: '所属科室',
      dataIndex: 'department_name',
      key: 'department_name'
    },
    {
      title: '字段数量',
      key: 'field_count',
      render: (_, record) => record.fields?.length || 0
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(record)}
          >
            复制
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确定要删除这个患者信息模板吗？',
                content: '删除后无法恢复',
                onOk: () => deleteMutation.mutate(record.id)
              })
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  // 移动端卡片视图渲染函数
  const renderMobileCard = (template) => {
    return (
      <Card
        key={template.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<FileTextOutlined />}
              style={{ backgroundColor: '#722ed1' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {template.name}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                ID: {template.id} | {template.fields?.length || 0} 个字段
              </div>
            </div>
          </div>
          <div style={{ display: 'flex', gap: 4, flexDirection: 'column', alignItems: 'flex-end' }}>
            {template.is_default && (
              <Tag color="blue" size="small">默认</Tag>
            )}
            <Tag color={template.is_active ? 'green' : 'red'} size="small">
              {template.is_active ? '启用' : '禁用'}
            </Tag>
          </div>
        </div>

        <div className="mobile-card-content">
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {template.description && (
              <div style={{ fontSize: 14, color: '#595959', marginBottom: 8 }}>
                {template.description}
              </div>
            )}
            {template.department_name && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TeamOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>科室: {template.department_name}</span>
              </div>
            )}
            {template.creator_name && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>创建者:</span>
                <span style={{ fontSize: 14 }}>{template.creator_name}</span>
              </div>
            )}
            {template.created_at && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>创建:</span>
                <span style={{ fontSize: 14 }}>{new Date(template.created_at).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>

        <div className="mobile-card-actions">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(template)}
          >
            编辑
          </Button>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(template)}
          >
            复制
          </Button>
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确定要删除这个患者信息模板吗？',
                content: '删除后无法恢复',
                onOk: () => deleteMutation.mutate(template.id)
              })
            }}
          >
            删除
          </Button>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 桌面端 */}
      {!isMobile && (
        <Card>
          <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
            <Col>
              <Title level={4} style={{ margin: 0 }}>患者信息模板管理</Title>
            </Col>
            <Col>
              <Space>
                <Button icon={<ReloadOutlined />} onClick={refetch}>
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建模板
                </Button>
              </Space>
            </Col>
          </Row>

          <Table
            columns={columns}
            dataSource={templatesData?.data || []}
            rowKey="id"
            loading={isLoading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: templatesData?.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              onChange: (page, pageSize) => {
                setPagination({ current: page, pageSize })
              }
            }}
          />
        </Card>
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              患者信息模板 ({templatesData?.total || 0})
            </Title>

            {isLoading ? (
              <Card loading style={{ marginBottom: 12 }} />
            ) : (
              <>
                {templatesData?.data && templatesData.data.length > 0 ? (
                  templatesData.data.map(renderMobileCard)
                ) : (
                  <Card style={{ textAlign: 'center', marginBottom: 12 }}>
                    <div style={{ padding: '20px 0', color: '#8c8c8c' }}>
                      暂无模板数据
                    </div>
                  </Card>
                )}

                {/* 移动端分页 */}
                {templatesData?.total > pagination.pageSize && (
                  <div style={{ textAlign: 'center', marginTop: 16, marginBottom: 80 }}>
                    <Button
                      disabled={pagination.current === 1}
                      onClick={() => setPagination({ ...pagination, current: pagination.current - 1 })}
                      style={{ marginRight: 8 }}
                    >
                      上一页
                    </Button>
                    <span style={{ margin: '0 16px', color: '#8c8c8c' }}>
                      {pagination.current} / {Math.ceil(templatesData.total / pagination.pageSize)}
                    </span>
                    <Button
                      disabled={pagination.current >= Math.ceil(templatesData.total / pagination.pageSize)}
                      onClick={() => setPagination({ ...pagination, current: pagination.current + 1 })}
                    >
                      下一页
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={handleCreate}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 模板编辑弹窗 */}
      <Modal
        title={editingTemplate ? '编辑患者信息模板' : '新建患者信息模板'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingTemplate(null)
          setTemplateFields([])
          setPreviewFields([])
          setShowAdvancedMode(false)
          form.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 800}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input
                  placeholder="请输入模板名称，如：产科患者信息模板"
                  onChange={(e) => {
                    if (!showAdvancedMode && !editingTemplate) {
                      handlePreviewFields(e.target.value)
                    }
                  }}
                />
              </Form.Item>
            </Col>
            {/* 只有超级管理员才能选择科室 */}
            {currentUser?.role === 'super_admin' ? (
              <Col span={isMobile ? 24 : 12}>
                <Form.Item
                  name="department_id"
                  label="所属科室"
                  rules={[{ required: true, message: '请选择所属科室' }]}
                >
                  <Select
                    placeholder="请选择所属科室"
                    loading={departmentsLoading}
                    notFoundContent={departmentsError ? '加载失败' : '暂无数据'}
                  >
                    {departmentsData?.departments?.map(dept => (
                      <Option key={dept.id} value={dept.id}>{dept.name}</Option>
                    ))}
                  </Select>
                  {departmentsError && (
                    <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
                      科室数据加载失败: {departmentsError.message}
                    </div>
                  )}
                  {!departmentsLoading && !departmentsError && (!departmentsData?.departments || departmentsData.departments.length === 0) && (
                    <div style={{ color: 'orange', fontSize: '12px', marginTop: '4px' }}>
                      暂无可用科室
                    </div>
                  )}
                </Form.Item>
              </Col>
            ) : (
              <Col span={isMobile ? 24 : 12}>
                <Form.Item label="所属科室">
                  <Input
                    value={currentUser?.department_name || '未分配科室'}
                    disabled
                    style={{ backgroundColor: '#f5f5f5' }}
                  />
                  <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                    模版将自动创建在您的科室下
                  </div>
                </Form.Item>
              </Col>
            )}
          </Row>

          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea rows={3} placeholder="请输入模板描述" />
          </Form.Item>

          {/* 模式切换 */}
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Text strong>配置模式：</Text>
              <Switch
                checked={showAdvancedMode}
                onChange={(checked) => {
                  setShowAdvancedMode(checked)
                  if (!checked) {
                    // 切换到简单模式时，如果有模板名称就预览字段
                    const templateName = form.getFieldValue('name')
                    if (templateName) {
                      handlePreviewFields(templateName)
                    }
                  }
                }}
                checkedChildren="高级模式"
                unCheckedChildren="简单模式"
              />
              <Text type="secondary">
                {showAdvancedMode ? '手动配置字段' : '根据模板名称自动生成字段'}
              </Text>
            </Space>
          </div>

          {/* 简单模式下的字段预览 */}
          {!showAdvancedMode && (
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>字段预览</Title>
              {isPreviewLoading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Spin tip="正在生成字段..." />
                </div>
              ) : previewFields.length > 0 ? (
                <div style={{
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  padding: '12px',
                  backgroundColor: '#fafafa',
                  maxHeight: '200px',
                  overflowY: 'auto'
                }}>
                  <Row gutter={[8, 8]}>
                    {previewFields.map((field, index) => (
                      <Col span={12} key={index}>
                        <Tag color={field.is_required ? 'red' : 'blue'}>
                          {field.field_label} ({field.field_type})
                          {field.is_required && ' *'}
                        </Tag>
                      </Col>
                    ))}
                  </Row>
                  <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
                    共 {previewFields.length} 个字段，红色为必填字段
                  </div>
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '20px',
                  color: '#999',
                  border: '1px dashed #d9d9d9',
                  borderRadius: '6px'
                }}>
                  请输入模板名称以预览自动生成的字段
                </div>
              )}
            </div>
          )}

          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="is_active"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="is_default"
                label="设为默认"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>

          {/* 高级模式下的字段配置 */}
          {showAdvancedMode && (
            <>
              <Divider>字段配置</Divider>

              <div style={{ marginBottom: 16 }}>
                <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddField}>
                  添加字段
                </Button>
              </div>
            </>
          )}

          {showAdvancedMode && templateFields.length > 0 && (
            <Table
              size="small"
              dataSource={templateFields}
              rowKey={(record, index) => index}
              pagination={false}
              columns={[
                {
                  title: '字段名称',
                  dataIndex: 'field_name',
                  key: 'field_name'
                },
                {
                  title: '字段标签',
                  dataIndex: 'field_label',
                  key: 'field_label'
                },
                {
                  title: '字段类型',
                  dataIndex: 'field_type',
                  key: 'field_type',
                  render: (type) => {
                    const option = fieldTypeOptions.find(opt => opt.value === type)
                    return option ? option.label : type
                  }
                },
                {
                  title: '必填',
                  dataIndex: 'is_required',
                  key: 'is_required',
                  render: (required) => required ? <Tag color="red">必填</Tag> : <Tag>可选</Tag>
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 120,
                  render: (_, record, index) => (
                    <Space size="small">
                      <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditField(index)}
                      >
                        编辑
                      </Button>
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteField(index)}
                      >
                        删除
                      </Button>
                    </Space>
                  )
                }
              ]}
            />
          )}

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => {
                setIsModalVisible(false)
                setEditingTemplate(null)
                setTemplateFields([])
                setPreviewFields([])
                setShowAdvancedMode(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={createMutation.isLoading || updateMutation.isLoading}>
                {editingTemplate ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 字段编辑弹窗 */}
      <Modal
        title={editingFieldIndex >= 0 ? '编辑字段' : '添加字段'}
        open={isFieldModalVisible}
        onCancel={() => {
          setIsFieldModalVisible(false)
          fieldForm.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 600}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={fieldForm}
          layout="vertical"
          onFinish={handleFieldSubmit}
        >
          {/* 隐藏的字段名称，自动生成 */}
          <Form.Item name="field_name" style={{ display: 'none' }}>
            <Input />
          </Form.Item>

          <Form.Item
            name="field_label"
            label="字段标签"
            rules={[{ required: true, message: '请输入字段标签' }]}
          >
            <Input
              placeholder="如: 出生体重"
              onChange={(e) => {
                const label = e.target.value
                if (label) {
                  // 自动生成字段名称
                  const fieldName = generateFieldName(label, templateFields)
                  fieldForm.setFieldsValue({ field_name: fieldName })
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="field_type"
            label="字段类型"
            rules={[{ required: true, message: '请选择字段类型' }]}
          >
            <Select>
              {fieldTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 选项配置 - 仅对 select, radio, checkbox 类型显示 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.field_type !== currentValues.field_type
            }
          >
            {({ getFieldValue }) => {
              const fieldType = getFieldValue('field_type')
              if (['select', 'radio', 'checkbox'].includes(fieldType)) {
                return (
                  <Form.Item
                    name={['field_config', 'options']}
                    label="选项配置"
                    rules={[{ required: true, message: '请至少添加一个选项' }]}
                  >
                    <Form.List name={['field_config', 'options']}>
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                              <Form.Item
                                {...restField}
                                name={name}
                                rules={[{ required: true, message: '请输入选项内容' }]}
                              >
                                <Input placeholder="选项内容" />
                              </Form.Item>
                              <MinusCircleOutlined onClick={() => remove(name)} />
                            </Space>
                          ))}
                          <Form.Item>
                            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                              添加选项
                            </Button>
                          </Form.Item>
                        </>
                      )}
                    </Form.List>
                  </Form.Item>
                )
              }
              return null
            }}
          </Form.Item>

          <Form.Item
            name="is_required"
            label="是否必填"
            valuePropName="checked"
            initialValue={false}
          >
            <Checkbox>必填字段</Checkbox>
          </Form.Item>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsFieldModalVisible(false)
                fieldForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingFieldIndex >= 0 ? '更新' : '添加'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default PatientTemplates
