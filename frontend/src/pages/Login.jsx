import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Form, Input, Button, message, Typography, Card } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useQueryClient } from 'react-query'
import { authService } from '../services/auth'

const { Title, Text } = Typography

const Login = () => {
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  const onFinish = async (values) => {
    setLoading(true)
    try {
      console.log('开始登录:', values)
      const response = await authService.login(values)
      console.log('登录响应:', response)

      // 显示成功消息
      message.success('登录成功')

      // 更新 React Query 缓存
      queryClient.setQueryData('currentUser', response.user)
      console.log('已更新缓存，用户数据:', response.user)

      // 使用 window.location 强制跳转
      console.log('使用 window.location 跳转')
      window.location.href = '/'

    } catch (error) {
      console.error('登录错误:', error)
      message.error(error.message || '登录失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <Card className="login-form">
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title
            level={2}
            style={{
              color: '#1890ff',
              marginBottom: 8,
              fontSize: window.innerWidth <= 768 ? '20px' : '24px'
            }}
          >
            涟源市妇幼保健院医院回访系统
          </Title>
          <Text type="secondary" style={{ fontSize: window.innerWidth <= 768 ? '13px' : '14px' }}>
            请输入您的账号和密码
          </Text>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              {
                required: true,
                message: '请输入用户名',
              },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              style={{
                height: window.innerWidth <= 768 ? '48px' : '40px',
                fontSize: window.innerWidth <= 768 ? '16px' : '14px'
              }}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: '请输入密码',
              },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              style={{
                height: window.innerWidth <= 768 ? '48px' : '40px',
                fontSize: window.innerWidth <= 768 ? '16px' : '14px'
              }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{
                height: window.innerWidth <= 768 ? '48px' : '40px',
                fontSize: window.innerWidth <= 768 ? '16px' : '14px',
                fontWeight: '500'
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text
            type="secondary"
            style={{
              fontSize: window.innerWidth <= 768 ? '11px' : '12px',
              lineHeight: '1.4'
            }}
          >
            账号请联系管理员
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default Login
