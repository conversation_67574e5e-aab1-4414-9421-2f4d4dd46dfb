import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Select,
  Card,
  Avatar,
  FloatButton
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  TeamOutlined,
  PhoneOutlined,
  MailOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import { departmentService } from '../services/departments'
import { userService } from '../services/users'

const { Title } = Typography
const { TextArea } = Input
const { Option } = Select

const Departments = () => {
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingDept, setEditingDept] = useState(null)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 获取科室列表
  const { data: deptsData, isLoading, refetch } = useQuery(
    ['departments', pagination.current, pagination.pageSize],
    async () => {
      const response = await departmentService.getDepartments({
        page: pagination.current,
        per_page: pagination.pageSize
      })
      return response
    },
    {
      keepPreviousData: true,
    }
  )

  // 获取用户列表（用于选择科室负责人）
  const { data: usersData } = useQuery(
    'allUsers',
    () => userService.getUsers({ per_page: 100 })
  )

  // 创建/更新科室
  const deptMutation = useMutation(
    (deptData) => {
      if (editingDept) {
        return departmentService.updateDepartment(editingDept.id, deptData)
      } else {
        return departmentService.createDepartment(deptData)
      }
    },
    {
      onSuccess: () => {
        message.success(editingDept ? '科室更新成功' : '科室创建成功')
        setIsModalVisible(false)
        setEditingDept(null)
        form.resetFields()
        queryClient.invalidateQueries('departments')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 删除科室
  const deleteMutation = useMutation(
    (deptId) => departmentService.deleteDepartment(deptId),
    {
      onSuccess: () => {
        message.success('科室删除成功')
        queryClient.invalidateQueries('departments')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  const columns = [
    {
      title: '科室名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '科室编码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '负责人',
      dataIndex: 'manager_name',
      key: 'manager_name',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '用户数量',
      dataIndex: 'users_count',
      key: 'users_count',
    },
    {
      title: '模板数量',
      dataIndex: 'templates_count',
      key: 'templates_count',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个科室吗？"
            description="删除后该科室下的用户和数据将无法访问"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              disabled={record.users_count > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleEdit = (dept) => {
    setEditingDept(dept)
    form.setFieldsValue(dept)
    setIsModalVisible(true)
  }

  const handleSubmit = (values) => {
    deptMutation.mutate(values)
  }

  const handleTableChange = (newPagination) => {
    setPagination(newPagination)
  }

  // 移动端卡片视图渲染函数
  const renderMobileCard = (dept) => {
    return (
      <Card
        key={dept.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<TeamOutlined />}
              style={{ backgroundColor: '#52c41a' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {dept.name}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                {dept.code} | ID: {dept.id}
              </div>
            </div>
          </div>
          <div style={{ display: 'flex', gap: 4, flexDirection: 'column', alignItems: 'flex-end' }}>
            <Tag color={dept.is_active ? 'green' : 'red'} size="small">
              {dept.is_active ? '启用' : '禁用'}
            </Tag>
            <div style={{ fontSize: 12, color: '#8c8c8c' }}>
              {dept.users_count || 0}人 | {dept.templates_count || 0}模板
            </div>
          </div>
        </div>

        <div className="mobile-card-content">
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {dept.description && (
              <div style={{ fontSize: 14, color: '#595959', marginBottom: 8 }}>
                {dept.description}
              </div>
            )}
            {dept.manager_name && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <UserOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>负责人: {dept.manager_name}</span>
              </div>
            )}
            {dept.phone && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <PhoneOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>{dept.phone}</span>
              </div>
            )}
            {dept.email && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <MailOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />
                <span style={{ fontSize: 14 }}>{dept.email}</span>
              </div>
            )}
            {dept.address && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>地址:</span>
                <span style={{ fontSize: 14 }}>{dept.address}</span>
              </div>
            )}
            {dept.created_at && (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ color: '#8c8c8c', fontSize: 14 }}>创建:</span>
                <span style={{ fontSize: 14 }}>{new Date(dept.created_at).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>

        <div className="mobile-card-actions">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(dept)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个科室吗？"
            description="删除后该科室下的用户和数据将无法访问"
            onConfirm={() => deleteMutation.mutate(dept.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={dept.users_count > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 桌面端页面头部 */}
      {!isMobile && (
        <div className="page-header">
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} className="page-title">科室管理</Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingDept(null)
                    form.resetFields()
                    setIsModalVisible(true)
                  }}
                >
                  新增科室
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      )}

      {/* 桌面端表格 */}
      {!isMobile && (
        <Table
          columns={columns}
          dataSource={deptsData?.departments || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: deptsData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              科室管理 ({deptsData?.total || 0})
            </Title>

            {isLoading ? (
              <Card loading style={{ marginBottom: 12 }} />
            ) : (
              <>
                {deptsData?.departments && deptsData.departments.length > 0 ? (
                  deptsData.departments.map(renderMobileCard)
                ) : (
                  <Card style={{ textAlign: 'center', marginBottom: 12 }}>
                    <div style={{ padding: '20px 0', color: '#8c8c8c' }}>
                      暂无科室数据
                    </div>
                  </Card>
                )}

                {/* 移动端分页 */}
                {deptsData?.total > pagination.pageSize && (
                  <div style={{ textAlign: 'center', marginTop: 16, marginBottom: 80 }}>
                    <Button
                      disabled={pagination.current === 1}
                      onClick={() => setPagination({ ...pagination, current: pagination.current - 1 })}
                      style={{ marginRight: 8 }}
                    >
                      上一页
                    </Button>
                    <span style={{ margin: '0 16px', color: '#8c8c8c' }}>
                      {pagination.current} / {Math.ceil(deptsData.total / pagination.pageSize)}
                    </span>
                    <Button
                      disabled={pagination.current >= Math.ceil(deptsData.total / pagination.pageSize)}
                      onClick={() => setPagination({ ...pagination, current: pagination.current + 1 })}
                    >
                      下一页
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => {
            setEditingDept(null)
            form.resetFields()
            setIsModalVisible(true)
          }}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 新增/编辑科室弹窗 */}
      <Modal
        title={editingDept ? '编辑科室' : '新增科室'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingDept(null)
          form.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 600}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="name"
                label="科室名称"
                rules={[{ required: true, message: '请输入科室名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="code"
                label="科室编码"
                rules={[{ required: true, message: '请输入科室编码' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="manager_id"
            label="科室负责人"
          >
            <Select allowClear placeholder="请选择负责人">
              {usersData?.users?.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.real_name || user.username} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="phone"
                label="联系电话"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="地址"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={deptMutation.isLoading}
              >
                {editingDept ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false)
                  setEditingDept(null)
                  form.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Departments
