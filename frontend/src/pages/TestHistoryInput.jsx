import React, { useState, useEffect } from 'react'
import { Card, Form, Button, Space, message, Input } from 'antd'
import HistoryInput from '../components/HistoryInput'

const TestHistoryInput = () => {
  const [form] = Form.useForm()
  const [testValue, setTestValue] = useState('')

  useEffect(() => {
    console.log('TestHistoryInput 组件加载')
  }, [])

  const handleSubmit = (values) => {
    console.log('表单提交:', values)
    message.success('提交成功: ' + JSON.stringify(values))
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="HistoryInput 组件测试">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="普通输入框（对比）"
            name="normal_input"
          >
            <Input placeholder="普通输入框" />
          </Form.Item>

          <Form.Item
            label="患者姓名"
            name="patient_name"
            rules={[{ required: true, message: '请输入患者姓名' }]}
          >
            <HistoryInput
              fieldName="patient_name"
              templateType="patient"
              templateId={1}
              type="text"
              placeholder="请输入患者姓名"
            />
          </Form.Item>

          <Form.Item
            label="联系电话"
            name="phone"
          >
            <HistoryInput
              fieldName="phone"
              templateType="patient"
              templateId={1}
              type="text"
              placeholder="请输入联系电话"
            />
          </Form.Item>

          <Form.Item
            label="家庭住址"
            name="address"
          >
            <HistoryInput
              fieldName="address"
              templateType="patient"
              templateId={1}
              type="textarea"
              rows={3}
              placeholder="请输入家庭住址"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交测试
              </Button>
              <Button onClick={() => form.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Card title="独立测试" style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <label>独立 HistoryInput (不在 Form 中):</label>
              <HistoryInput
                fieldName="test_field"
                templateType="patient"
                templateId={1}
                type="text"
                placeholder="独立测试输入框"
                value={testValue}
                onChange={setTestValue}
              />
            </div>
            <div>当前值: {testValue}</div>
          </Space>
        </Card>
      </Card>
    </div>
  )
}

export default TestHistoryInput
