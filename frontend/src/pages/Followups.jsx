import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Card,
  Descriptions,
  Radio,
  FloatButton,
  Avatar,
  Badge,
  Tabs
} from 'antd'
import HistoryInput from '../components/HistoryInput'
import BatchCreateFollowup from '../components/BatchCreateFollowup'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  PhoneOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'
import { followupService } from '../services/followups'
import { templateService } from '../services/templates'
import { authService } from '../services/auth'
import api from '../services/api'

const { Title } = Typography
const { TextArea } = Input
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

const Followups = () => {
  const [form] = Form.useForm()
  const [executeForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isExecuteModalVisible, setIsExecuteModalVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测是否为移动端
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])
  const [isViewModalVisible, setIsViewModalVisible] = useState(false)
  const [editingFollowup, setEditingFollowup] = useState(null)
  const [executingFollowup, setExecutingFollowup] = useState(null)
  const [viewingFollowup, setViewingFollowup] = useState(null)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 })
  const [filters, setFilters] = useState({})
  const [searchText, setSearchText] = useState('')
  const [dateRange, setDateRange] = useState(null)
  const [exportLoading, setExportLoading] = useState(false)
  const [isBatchCreateVisible, setIsBatchCreateVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('pending') // 当前激活的标签页

  // 获取回访记录列表
  const { data: followupsData, isLoading, refetch } = useQuery(
    ['followups', pagination.current, pagination.pageSize, filters, searchText, activeTab],
    () => {
      // 根据当前标签页设置状态过滤
      const tabFilters = { ...filters }
      if (activeTab === 'pending') {
        tabFilters.status = 'pending'
      } else if (activeTab === 'completed') {
        tabFilters.status = 'completed'
      }

      return followupService.getFollowups({
        page: pagination.current,
        per_page: pagination.pageSize,
        search: searchText,
        ...tabFilters
      })
    },
    {
      keepPreviousData: true,
    }
  )

  // 获取当前用户信息
  const { data: currentUser } = useQuery('currentUser', authService.getCurrentUser)

  // 获取患者列表
  const { data: patientsData } = useQuery(
    'patients',
    () => api.get('/patients', { params: { per_page: 100 } })
  )

  // 获取模板列表
  const { data: templatesData } = useQuery(
    'templates',
    () => templateService.getTemplates({ per_page: 100 })
  )

  // 创建/更新回访记录
  const followupMutation = useMutation(
    (followupData) => {
      const data = { ...followupData }
      if (data.followup_date) {
        data.followup_date = data.followup_date.format('YYYY-MM-DD HH:mm:ss')
      }

      if (editingFollowup) {
        return followupService.updateFollowup(editingFollowup.id, data)
      } else {
        return followupService.createFollowup(data)
      }
    },
    {
      onSuccess: () => {
        message.success(editingFollowup ? '回访记录更新成功' : '回访记录创建成功')
        setIsModalVisible(false)
        setEditingFollowup(null)
        form.resetFields()
        queryClient.invalidateQueries('followups')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 执行回访
  const executeMutation = useMutation(
    ({ followupId, data }) => followupService.executeFollowup(followupId, data),
    {
      onSuccess: () => {
        message.success('回访执行成功，已移至已回访列表')
        setIsExecuteModalVisible(false)
        setExecutingFollowup(null)
        executeForm.resetFields()
        queryClient.invalidateQueries('followups')
        // 自动切换到已回访标签页
        setActiveTab('completed')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  // 删除回访记录
  const deleteMutation = useMutation(
    (followupId) => followupService.deleteFollowup(followupId),
    {
      onSuccess: () => {
        message.success('回访记录删除成功')
        queryClient.invalidateQueries('followups')
      },
      onError: (error) => {
        message.error(error.message)
      }
    }
  )

  const getStatusTag = (status, isOverdue) => {
    if (isOverdue) {
      return <Tag color="red" icon={<ExclamationCircleOutlined />}>已逾期</Tag>
    }
    
    const statusMap = {
      pending: <Tag color="orange" icon={<ClockCircleOutlined />}>待回访</Tag>,
      completed: <Tag color="green" icon={<CheckCircleOutlined />}>已完成</Tag>,
      failed: <Tag color="red">失败</Tag>,
      cancelled: <Tag color="default">已取消</Tag>
    }
    return statusMap[status] || <Tag>{status}</Tag>
  }

  const getMethodTag = (method) => {
    const methodMap = {
      phone: { text: '电话', color: 'blue' },
      sms: { text: '短信', color: 'green' },
      wechat: { text: '微信', color: 'cyan' },
      email: { text: '邮件', color: 'purple' },
      visit: { text: '上门', color: 'orange' }
    }
    const methodInfo = methodMap[method] || { text: method, color: 'default' }
    return <Tag color={methodInfo.color}>{methodInfo.text}</Tag>
  }

  const columns = [
    {
      title: '患者姓名',
      dataIndex: 'patient_name',
      key: 'patient_name',
      width: 100,
    },
    {
      title: '回访模板',
      dataIndex: 'template_name',
      key: 'template_name',
      width: 120,
    },
    {
      title: '回访方式',
      dataIndex: 'followup_method',
      key: 'followup_method',
      width: 80,
      render: (method) => getMethodTag(method)
    },
    {
      title: '计划时间',
      dataIndex: 'followup_date',
      key: 'followup_date',
      width: 150,
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '实际时间',
      dataIndex: 'actual_date',
      key: 'actual_date',
      width: 150,
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '待回访', value: 'pending' },
        { text: '已完成', value: 'completed' },
        { text: '失败', value: 'failed' },
        { text: '已取消', value: 'cancelled' }
      ],
      render: (_, record) => getStatusTag(record.status, record.is_overdue)
    },
    {
      title: '执行人',
      dataIndex: 'executor_name',
      key: 'executor_name',
      width: 100,
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 80,
      render: (result) => {
        const resultMap = {
          success: <Tag color="green">成功</Tag>,
          no_answer: <Tag color="orange">无人接听</Tag>,
          refused: <Tag color="red">拒绝</Tag>,
          invalid_number: <Tag color="red">号码无效</Tag>
        }
        return result ? resultMap[result] || <Tag>{result}</Tag> : '-'
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <Button
              type="primary"
              size="small"
              icon={<PhoneOutlined />}
              onClick={() => handleExecute(record)}
            >
              执行回访
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条回访记录吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleEdit = (followup) => {
    setEditingFollowup(followup)
    const formData = {
      ...followup,
      followup_date: followup.followup_date ? dayjs(followup.followup_date) : null
    }
    form.setFieldsValue(formData)
    setIsModalVisible(true)
  }

  const handleExecute = (followup) => {
    setExecutingFollowup(followup)
    executeForm.resetFields()
    setIsExecuteModalVisible(true)
  }

  const handleView = (followup) => {
    console.log('查看回访记录:', followup)
    setViewingFollowup(followup)
    setIsViewModalVisible(true)
  }

  const handleSubmit = (values) => {
    followupMutation.mutate(values)
  }

  const handleExecuteSubmit = async (values) => {
    // 批量记录字段历史
    if (executingFollowup?.template) {
      try {
        const fields = []
        executingFollowup.template.fields?.forEach(field => {
          const fieldValue = values.data?.[field.field_name]
          if (fieldValue && (field.field_type === 'text' || field.field_type === 'textarea')) {
            fields.push({
              field_name: field.field_name,
              field_value: fieldValue
            })
          }
        })

        if (fields.length > 0) {
          await api.post('/field-history/batch-record', {
            fields: fields,
            template_type: 'followup',
            template_id: executingFollowup.template.id
          })
        }
      } catch (error) {
        console.error('记录字段历史失败:', error)
        // 不影响主要流程，只记录错误
      }
    }

    executeMutation.mutate({
      followupId: executingFollowup.id,
      data: values
    })
  }

  const handleTableChange = (newPagination, tableFilters) => {
    setPagination(newPagination)

    // 处理筛选条件
    const newFilters = {}
    if (tableFilters.status && tableFilters.status.length > 0) {
      newFilters.status = tableFilters.status[0]
    }
    setFilters(newFilters)
  }

  const handleSearch = (value) => {
    setSearchText(value)
    setPagination({ ...pagination, current: 1 })
  }

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key)
    setPagination({ current: 1, pageSize: 20 }) // 重置分页
    setFilters({}) // 清除其他过滤条件
    setSearchText('') // 清除搜索条件
  }

  // 导出Excel功能
  const handleExport = async () => {
    try {
      setExportLoading(true)

      // 构建查询参数
      const params = new URLSearchParams()
      if (filters.status) params.append('status', filters.status)
      if (dateRange && dateRange[0]) params.append('date_from', dateRange[0].format('YYYY-MM-DD'))
      if (dateRange && dateRange[1]) params.append('date_to', dateRange[1].format('YYYY-MM-DD'))
      if (searchText) params.append('search', searchText)

      // 调用后端导出API
      const response = await api.get(`/followups/export?${params.toString()}`, {
        responseType: 'blob'
      })

      // 创建下载链接
      const blob = new Blob([response], { type: 'text/csv;charset=utf-8-sig' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `回访记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败，请重试')
    } finally {
      setExportLoading(false)
    }
  }

  // 处理批量创建成功
  const handleBatchCreateSuccess = () => {
    setIsBatchCreateVisible(false)
    refetch() // 刷新回访记录列表
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates)
    if (dates) {
      setFilters({
        ...filters,
        date_from: dates[0].format('YYYY-MM-DD'),
        date_to: dates[1].format('YYYY-MM-DD')
      })
    } else {
      const newFilters = { ...filters }
      delete newFilters.date_from
      delete newFilters.date_to
      setFilters(newFilters)
    }
    setPagination({ ...pagination, current: 1 })
  }

  const renderExecuteForm = () => {
    if (!executingFollowup?.template?.fields) return null

    return executingFollowup.template.fields.map((field) => {
      const commonProps = {
        key: field.field_name,
        name: ['data', field.field_name],
        label: field.field_label,
        rules: field.is_required ? [{ required: true, message: `请填写${field.field_label}` }] : []
      }

      switch (field.field_type) {
        case 'text':
          return (
            <Form.Item {...commonProps}>
              <HistoryInput
                fieldName={field.field_name}
                templateType="followup"
                templateId={executingFollowup?.template?.id}
                type="text"
                placeholder={`请输入${field.field_label}`}
              />
            </Form.Item>
          )
        case 'textarea':
          return (
            <Form.Item {...commonProps}>
              <HistoryInput
                fieldName={field.field_name}
                templateType="followup"
                templateId={executingFollowup?.template?.id}
                type="textarea"
                rows={3}
                placeholder={`请输入${field.field_label}`}
              />
            </Form.Item>
          )
        case 'number':
          return <Form.Item {...commonProps}><Input type="number" /></Form.Item>
        case 'select':
          return (
            <Form.Item {...commonProps}>
              <Select>
                {field.field_config?.options?.map(option => (
                  <Option key={option} value={option}>{option}</Option>
                ))}
              </Select>
            </Form.Item>
          )
        case 'radio':
          return (
            <Form.Item {...commonProps}>
              <Radio.Group>
                {field.field_config?.options?.map(option => (
                  <Radio key={option} value={option}>{option}</Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          )
        case 'date':
          return <Form.Item {...commonProps}><DatePicker style={{ width: '100%' }} /></Form.Item>
        case 'datetime':
          return <Form.Item {...commonProps}><DatePicker showTime style={{ width: '100%' }} /></Form.Item>
        default:
          return <Form.Item {...commonProps}><Input /></Form.Item>
      }
    })
  }

  // 移动端卡片视图渲染函数
  const renderMobileCard = (followup) => {
    const getStatusColor = (status) => {
      const colorMap = {
        'pending': 'orange',
        'completed': 'green',
        'failed': 'red',
        'cancelled': 'default'
      }
      return colorMap[status] || 'default'
    }

    const getStatusText = (status) => {
      const textMap = {
        'pending': '待回访',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
      }
      return textMap[status] || status
    }

    const getStatusIcon = (status) => {
      const iconMap = {
        'pending': <ClockCircleOutlined />,
        'completed': <CheckCircleOutlined />,
        'failed': <ExclamationCircleOutlined />,
        'cancelled': <ExclamationCircleOutlined />
      }
      return iconMap[status] || <ClockCircleOutlined />
    }

    return (
      <Card
        key={followup.id}
        className="mobile-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{ backgroundColor: '#52c41a' }}
            />
            <div>
              <div style={{ fontWeight: 500, fontSize: 16 }}>
                {followup.patient_name || '未知患者'}
              </div>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                ID: {followup.id} | {followup.template?.name || '无模板'}
              </div>
            </div>
          </div>
          <Badge
            status={getStatusColor(followup.status)}
            text={
              <Tag
                color={getStatusColor(followup.status)}
                icon={getStatusIcon(followup.status)}
                size="small"
              >
                {getStatusText(followup.status)}
              </Tag>
            }
          />
        </div>

        <div className="mobile-card-content">
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>回访日期</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                <CalendarOutlined style={{ marginRight: 4, color: '#1890ff' }} />
                {followup.followup_date ? dayjs(followup.followup_date).format('MM-DD') : '-'}
              </div>
            </Col>
            <Col span={12}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>回访方式</div>
              <div style={{ fontSize: 14, fontWeight: 500 }}>
                <PhoneOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                {followup.method === 'phone' ? '电话' : followup.method || '-'}
              </div>
            </Col>
            <Col span={24}>
              <div style={{ fontSize: 12, color: '#8c8c8c' }}>创建时间</div>
              <div style={{ fontSize: 14 }}>
                {followup.created_at ? dayjs(followup.created_at).format('YYYY-MM-DD HH:mm') : '-'}
              </div>
            </Col>
            {followup.notes && (
              <Col span={24}>
                <div style={{ fontSize: 12, color: '#8c8c8c' }}>备注</div>
                <div style={{ fontSize: 14 }}>
                  <FileTextOutlined style={{ marginRight: 4, color: '#faad14' }} />
                  {followup.notes}
                </div>
              </Col>
            )}
          </Row>
        </div>

        <div className="mobile-card-actions">
          {followup.status === 'pending' && (
            <Button
              type="primary"
              size="small"
              icon={<PhoneOutlined />}
              onClick={() => handleExecute(followup)}
            >
              执行回访
            </Button>
          )}
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(followup)}
          >
            查看
          </Button>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(followup)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条回访记录吗？"
            onConfirm={() => deleteMutation.mutate(followup.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 移动端搜索栏 */}
      {isMobile && (
        <div className="mobile-search-bar">
          <Input.Search
            placeholder="搜索患者姓名"
            allowClear
            onSearch={handleSearch}
            style={{ marginBottom: 8 }}
          />
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            style={{ width: '100%', marginBottom: 8 }}
            value={dateRange}
            onChange={handleDateRangeChange}
          />
          <Row gutter={8} style={{ marginTop: 8 }}>
            <Col span={12}>
              <Button
                block
                icon={<PlusOutlined />}
                onClick={() => setIsBatchCreateVisible(true)}
              >
                批量创建
              </Button>
            </Col>
            <Col span={12}>
              <Button
                block
                icon={<DownloadOutlined />}
                onClick={handleExport}
                loading={exportLoading}
              >
                导出Excel
              </Button>
            </Col>
          </Row>
        </div>
      )}

      {/* 桌面端页面标题 */}
      {!isMobile && (
        <div className="page-header">
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} className="page-title">回访管理</Title>
            </Col>
            <Col>
              <Space>
                <Input.Search
                  placeholder="搜索患者姓名"
                  allowClear
                  style={{ width: 200 }}
                  onSearch={handleSearch}
                  onChange={(e) => {
                    if (!e.target.value) {
                      handleSearch('')
                    }
                  }}
                />

                <RangePicker
                  placeholder={['开始日期', '结束日期']}
                  value={dateRange}
                  onChange={handleDateRangeChange}
                />
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  loading={exportLoading}
                >
                  导出Excel
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                >
                  刷新
                </Button>
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => setIsBatchCreateVisible(true)}
                >
                  批量创建
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingFollowup(null)
                    form.resetFields()

                    // 查找当前用户科室的默认模板并自动选择
                    let defaultTemplate = null
                    if (currentUser?.department_id) {
                      // 优先选择当前用户科室的默认模板
                      defaultTemplate = templatesData?.templates?.find(t =>
                        t.is_default && t.department_id === currentUser.department_id
                      )
                    }

                    // 如果没有找到科室默认模板，则选择任意默认模板
                    if (!defaultTemplate) {
                      defaultTemplate = templatesData?.templates?.find(t => t.is_default)
                    }

                    if (defaultTemplate) {
                      form.setFieldsValue({ template_id: defaultTemplate.id })
                      console.log('自动选择默认回访模板:', defaultTemplate.name, '(科室:', defaultTemplate.department_name, ')')
                    }

                    setIsModalVisible(true)
                  }}
                >
                  新增回访
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      )}

      {/* 桌面端表格 */}
      {!isMobile && (
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          type="card"
          style={{ marginTop: 16 }}
        >
          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                未回访
              </span>
            }
            key="pending"
          >
            <Table
              columns={columns}
              dataSource={followupsData?.followups || []}
              rowKey="id"
              loading={isLoading}
              scroll={{ x: 1200 }}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: followupsData?.total || 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              onChange={handleTableChange}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                已回访
              </span>
            }
            key="completed"
          >
            <Table
              columns={columns}
              dataSource={followupsData?.followups || []}
              rowKey="id"
              loading={isLoading}
              scroll={{ x: 1200 }}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: followupsData?.total || 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              onChange={handleTableChange}
            />
          </TabPane>
        </Tabs>
      )}

      {/* 移动端列表视图 */}
      {isMobile && (
        <div className="mobile-list">
          <div style={{ padding: '0 8px' }}>
            <Title level={5} style={{ margin: '16px 0 8px 0', textAlign: 'center' }}>
              回访管理 ({followupsData?.total || 0})
            </Title>

            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              type="card"
              size="small"
              style={{ marginBottom: 16 }}
            >
              <TabPane
                tab={
                  <span>
                    <ClockCircleOutlined />
                    未回访
                  </span>
                }
                key="pending"
              >
                {isLoading ? (
                  <Card loading style={{ marginBottom: 12 }} />
                ) : (
                  <>
                    {followupsData?.followups && followupsData.followups.length > 0 ? (
                      followupsData.followups.map(renderMobileCard)
                    ) : (
                      <Card style={{ textAlign: 'center', marginBottom: 12 }}>
                        <div style={{ padding: '20px 0', color: '#8c8c8c' }}>
                          暂无未回访记录
                        </div>
                      </Card>
                    )}

                    {/* 移动端分页 */}
                    {followupsData?.total > pagination.pageSize && (
                      <Card size="small" style={{ textAlign: 'center', marginTop: 16 }}>
                        <Space>
                          <Button
                            size="small"
                            disabled={pagination.current === 1}
                            onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                          >
                            上一页
                          </Button>
                          <span style={{ fontSize: 12, color: '#8c8c8c' }}>
                            {pagination.current} / {Math.ceil((followupsData?.total || 0) / pagination.pageSize)}
                          </span>
                          <Button
                            size="small"
                            disabled={pagination.current >= Math.ceil((followupsData?.total || 0) / pagination.pageSize)}
                            onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                          >
                            下一页
                          </Button>
                        </Space>
                      </Card>
                    )}
                  </>
                )}
              </TabPane>
              <TabPane
                tab={
                  <span>
                    <CheckCircleOutlined />
                    已回访
                  </span>
                }
                key="completed"
              >
                {isLoading ? (
                  <Card loading style={{ marginBottom: 12 }} />
                ) : (
                  <>
                    {followupsData?.followups && followupsData.followups.length > 0 ? (
                      followupsData.followups.map(renderMobileCard)
                    ) : (
                      <Card style={{ textAlign: 'center', marginBottom: 12 }}>
                        <div style={{ padding: '20px 0', color: '#8c8c8c' }}>
                          暂无已回访记录
                        </div>
                      </Card>
                    )}

                    {/* 移动端分页 */}
                    {followupsData?.total > pagination.pageSize && (
                      <Card size="small" style={{ textAlign: 'center', marginTop: 16 }}>
                        <Space>
                          <Button
                            size="small"
                            disabled={pagination.current === 1}
                            onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                          >
                            上一页
                          </Button>
                          <span style={{ fontSize: 12, color: '#8c8c8c' }}>
                            {pagination.current} / {Math.ceil((followupsData?.total || 0) / pagination.pageSize)}
                          </span>
                          <Button
                            size="small"
                            disabled={pagination.current >= Math.ceil((followupsData?.total || 0) / pagination.pageSize)}
                            onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                          >
                            下一页
                          </Button>
                        </Space>
                      </Card>
                    )}
                  </>
                )}
              </TabPane>
            </Tabs>
          </div>
        </div>
      )}

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => {
            setEditingFollowup(null)
            form.resetFields()

            // 查找当前用户科室的默认模板并自动选择
            let defaultTemplate = null
            if (currentUser?.department_id) {
              defaultTemplate = templatesData?.templates?.find(t =>
                t.is_default && t.department_id === currentUser.department_id
              )
            }

            if (!defaultTemplate) {
              defaultTemplate = templatesData?.templates?.find(t => t.is_default)
            }

            if (defaultTemplate) {
              form.setFieldsValue({ template_id: defaultTemplate.id })
            }

            setIsModalVisible(true)
          }}
          style={{ right: 24, bottom: 24 }}
        />
      )}

      {/* 新增/编辑回访记录弹窗 */}
      <Modal
        title={editingFollowup ? '编辑回访记录' : '新增回访记录'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingFollowup(null)
          form.resetFields()
        }}
        footer={null}
        width={isMobile ? '100%' : 600}
        style={isMobile ? { top: 0, paddingBottom: 0 } : {}}
        styles={{
          body: isMobile ? {
            padding: '20px 16px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto'
          } : {}
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="patient_id"
                label="患者"
                rules={[{ required: true, message: '请选择患者' }]}
              >
                <Select placeholder="请选择患者" showSearch>
                  {patientsData?.patients?.map(patient => (
                    <Option key={patient.id} value={patient.id}>
                      {patient.name} - {patient.phone}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="template_id"
                label="回访模板"
                rules={[{ required: true, message: '请选择回访模板' }]}
              >
                <Select placeholder="请选择回访模板">
                  {templatesData?.templates?.map(template => (
                    <Option key={template.id} value={template.id}>
                      {template.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="followup_date"
                label="回访时间"
                rules={[{ required: true, message: '请选择回访时间' }]}
              >
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="followup_method"
                label="回访方式"
              >
                <Select placeholder="请选择回访方式">
                  <Option value="phone">电话</Option>
                  <Option value="sms">短信</Option>
                  <Option value="wechat">微信</Option>
                  <Option value="email">邮件</Option>
                  <Option value="visit">上门</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={followupMutation.isLoading}
              >
                {editingFollowup ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false)
                  setEditingFollowup(null)
                  form.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 执行回访弹窗 */}
      <Modal
        title="执行回访"
        open={isExecuteModalVisible}
        onCancel={() => {
          setIsExecuteModalVisible(false)
          setExecutingFollowup(null)
          executeForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        {executingFollowup && (
          <>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Descriptions size="small" column={2}>
                <Descriptions.Item label="患者">{executingFollowup.patient_name}</Descriptions.Item>
                <Descriptions.Item label="模板">{executingFollowup.template_name}</Descriptions.Item>
                <Descriptions.Item label="方式">{getMethodTag(executingFollowup.followup_method)}</Descriptions.Item>
                <Descriptions.Item label="时间">
                  {dayjs(executingFollowup.followup_date).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Form
              form={executeForm}
              layout="vertical"
              onFinish={handleExecuteSubmit}
            >
              <Form.Item
                name="result"
                label="回访结果"
                rules={[{ required: true, message: '请选择回访结果' }]}
              >
                <Radio.Group>
                  <Radio value="success">成功</Radio>
                  <Radio value="no_answer">无人接听</Radio>
                  <Radio value="refused">拒绝回访</Radio>
                  <Radio value="invalid_number">号码无效</Radio>
                </Radio.Group>
              </Form.Item>

              {renderExecuteForm()}

              <Form.Item
                name="notes"
                label="备注"
              >
                <TextArea rows={3} placeholder="请输入回访备注信息" />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={executeMutation.isLoading}
                  >
                    完成回访
                  </Button>
                  <Button
                    onClick={() => {
                      setIsExecuteModalVisible(false)
                      setExecutingFollowup(null)
                      executeForm.resetFields()
                    }}
                  >
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>

      {/* 查看回访详情弹窗 */}
      <Modal
        title={
          <div>
            <span>回访详情</span>
            {viewingFollowup && (
              <span style={{ fontSize: '12px', color: '#666', marginLeft: '16px' }}>
                创建时间: {dayjs(viewingFollowup.created_at).format('YYYY-MM-DD HH:mm')}
              </span>
            )}
          </div>
        }
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false)
          setViewingFollowup(null)
        }}
        footer={[
          <Button key="close" onClick={() => {
            setIsViewModalVisible(false)
            setViewingFollowup(null)
          }}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {viewingFollowup && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Descriptions size="small" column={2} bordered>
                <Descriptions.Item label="患者姓名">
                  <strong>{viewingFollowup.patient_name}</strong>
                </Descriptions.Item>
                <Descriptions.Item label="回访模板">{viewingFollowup.template_name}</Descriptions.Item>
                <Descriptions.Item label="回访方式">{viewingFollowup.followup_method}</Descriptions.Item>
                <Descriptions.Item label="计划时间">
                  {viewingFollowup.followup_date ? dayjs(viewingFollowup.followup_date).format('YYYY-MM-DD HH:mm') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="实际时间">
                  {viewingFollowup.actual_date ? dayjs(viewingFollowup.actual_date).format('YYYY-MM-DD HH:mm') : '未执行'}
                </Descriptions.Item>
                <Descriptions.Item label="回访状态">{viewingFollowup.status}</Descriptions.Item>
                <Descriptions.Item label="回访结果">{viewingFollowup.result || '无'}</Descriptions.Item>
                <Descriptions.Item label="执行人">{viewingFollowup.executor_name || '未分配'}</Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 回访内容 */}
            {viewingFollowup.template && viewingFollowup.template.fields && viewingFollowup.template.fields.length > 0 && (
              <Card title="回访内容" size="small" style={{ marginBottom: 16 }}>
                <Descriptions size="small" column={1} bordered>
                  {viewingFollowup.template.fields.map((field) => {
                    const value = viewingFollowup.data && viewingFollowup.data[field.field_name]
                    return (
                      <Descriptions.Item key={field.field_name} label={field.field_label}>
                        {value || '未填写'}
                      </Descriptions.Item>
                    )
                  })}
                </Descriptions>
              </Card>
            )}

            {/* 备注信息 */}
            <Card title="备注信息" size="small">
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#fafafa',
                borderRadius: '4px',
                minHeight: '40px'
              }}>
                {viewingFollowup.notes || '无备注信息'}
              </div>
            </Card>
          </div>
        )}
      </Modal>

      {/* 批量创建回访记录弹窗 */}
      <BatchCreateFollowup
        visible={isBatchCreateVisible}
        onCancel={() => setIsBatchCreateVisible(false)}
        onSuccess={handleBatchCreateSuccess}
      />
    </div>
  )
}

export default Followups
