/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

#root {
  min-height: 100vh;
}

/* 自定义样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.main-layout {
  min-height: 100vh;
}

.main-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.main-content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  min-height: calc(100vh - 112px);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-tag {
  font-size: 12px;
}

.overdue-tag {
  color: #ff4d4f;
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.pending-tag {
  color: #faad14;
  background-color: #fffbe6;
  border-color: #ffe58f;
}

.completed-tag {
  color: #52c41a;
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.failed-tag {
  color: #ff4d4f;
  background-color: #fff2f0;
  border-color: #ffccc7;
}

/* 用户信息显示样式 - 简洁版本 */
.header-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.header-user-info:hover {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
}

.header-user-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.header-user-name {
  font-weight: 500;
  color: #262626;
}

.header-user-separator {
  color: #8c8c8c;
}

.header-user-department {
  color: #595959;
  font-size: 13px;
}

.header-user-role-tag {
  margin: 0;
  font-size: 11px;
  line-height: 18px;
  height: 20px;
}

/* 移动端优化样式 */
.mobile-card {
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.mobile-card-content {
  padding: 12px 16px;
}

.mobile-card-actions {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.mobile-form-item {
  margin-bottom: 16px;
}

.mobile-search-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin: -24px -24px 16px -24px;
}

.mobile-fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-list-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
  margin-bottom: 1px;
}

.mobile-list-item:last-child {
  border-bottom: none;
}

.mobile-list-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.mobile-list-item-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.mobile-list-item-meta-item {
  font-size: 12px;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.mobile-list-item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin: 8px;
    padding: 0;
    background: #f5f5f5;
    border-radius: 0;
  }

  .login-form {
    width: 90%;
    padding: 30px 20px;
    margin: 20px;
  }

  .login-container {
    padding: 20px;
  }

  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 16px;
    background: white;
    margin-bottom: 8px;
    border-radius: 8px;
  }

  .search-form {
    flex-wrap: wrap;
    gap: 8px;
  }

  .page-header {
    margin: 0;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
    margin-bottom: 8px;
  }

  .page-title {
    font-size: 18px;
  }

  /* 隐藏桌面端的表格，显示移动端卡片 */
  .ant-table-wrapper {
    display: none !important;
  }

  .mobile-list {
    display: block !important;
  }

  .mobile-search-bar {
    display: block !important;
  }

  .mobile-card {
    display: block !important;
  }

  /* 表单优化 */
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-picker {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .ant-btn {
    height: 44px;
    font-size: 16px;
  }

  .ant-btn-sm {
    height: 32px;
    font-size: 14px;
  }

  /* 模态框优化 */
  .ant-modal {
    margin: 0;
    max-width: 100vw;
    top: 0;
  }

  .ant-modal-content {
    border-radius: 0;
    min-height: 100vh;
  }

  .ant-modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    padding: 20px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .ant-modal-footer {
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
  }

  .header-user-info {
    padding: 4px 8px;
    gap: 6px;
  }

  .header-user-details {
    gap: 4px;
    font-size: 12px;
  }

  .header-user-department {
    font-size: 11px;
  }

  .header-user-role-tag {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
  }
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  /* 触摸目标大小优化 */
  .ant-btn-sm {
    min-height: 32px;
    padding: 4px 12px;
  }

  .ant-tag {
    padding: 2px 8px;
    line-height: 18px;
  }

  /* 卡片间距优化 */
  .ant-card-small > .ant-card-head {
    padding: 0 12px;
    min-height: 48px;
  }

  .ant-card-small > .ant-card-body {
    padding: 12px;
  }

  /* 表单标签优化 */
  .ant-form-item-label > label {
    font-size: 14px;
    font-weight: 500;
  }

  /* 选择器下拉优化 */
  .ant-select-dropdown {
    font-size: 16px;
  }

  .ant-select-item {
    padding: 8px 12px;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* 日期选择器优化 */
  .ant-picker-dropdown {
    font-size: 16px;
  }

  .ant-picker-cell {
    height: 44px;
    line-height: 44px;
  }

  /* 消息提示优化 */
  .ant-message {
    font-size: 16px;
  }

  /* 抽屉优化 */
  .ant-drawer-body {
    padding: 0;
  }

  .ant-drawer-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 下拉菜单优化 */
  .ant-dropdown-menu {
    font-size: 16px;
  }

  .ant-dropdown-menu-item {
    padding: 8px 16px;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* 分页器优化 */
  .ant-pagination {
    text-align: center;
  }

  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next {
    min-width: 44px;
    height: 44px;
    line-height: 42px;
  }

  /* 加载状态优化 */
  .ant-spin-dot {
    font-size: 24px;
  }

  /* 空状态优化 */
  .ant-empty {
    padding: 40px 20px;
  }

  .ant-empty-description {
    font-size: 14px;
    color: #8c8c8c;
  }
}

/* 桌面端隐藏移动端组件 */
@media (min-width: 769px) {
  .mobile-list {
    display: none !important;
  }

  .mobile-fab {
    display: none !important;
  }

  .mobile-search-bar {
    display: none !important;
  }

  .mobile-card {
    display: none !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .login-form {
    width: 95%;
    padding: 24px 16px;
    margin: 16px;
  }

  .main-content {
    margin: 4px;
    padding: 0;
  }

  .mobile-search-bar {
    padding: 8px 12px;
    margin: -24px -24px 12px -24px;
  }

  .mobile-card-header {
    padding: 8px 12px;
  }

  .mobile-card-content {
    padding: 8px 12px;
  }

  .mobile-card-actions {
    padding: 6px 12px;
    gap: 6px;
  }

  .page-title {
    font-size: 16px;
  }

  .ant-modal-header {
    padding: 12px 16px;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .ant-modal-footer {
    padding: 12px 16px;
  }
}
