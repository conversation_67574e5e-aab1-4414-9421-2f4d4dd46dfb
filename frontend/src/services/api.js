import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // 清除本地存储的token
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      // 重定向到登录页
      window.location.href = '/login'
    }
    
    // 返回错误信息
    const message = error.response?.data?.error || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

// 患者信息模板相关API
export const getPatientTemplates = (params = {}) => {
  return api.get('/patient-templates', { params })
}

export const createPatientTemplate = (data) => {
  return api.post('/patient-templates', data)
}

export const updatePatientTemplate = (id, data) => {
  return api.put(`/patient-templates/${id}`, data)
}

export const deletePatientTemplate = (id) => {
  return api.delete(`/patient-templates/${id}`)
}

export default api
