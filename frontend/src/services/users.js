import api from './api'

export const userService = {
  // 获取用户列表
  getUsers: async (params = {}) => {
    return await api.get('/users', { params })
  },

  // 创建用户
  createUser: async (userData) => {
    return await api.post('/users', userData)
  },

  // 更新用户
  updateUser: async (userId, userData) => {
    return await api.put(`/users/${userId}`, userData)
  },

  // 删除用户
  deleteUser: async (userId) => {
    return await api.delete(`/users/${userId}`)
  },

  // 重置密码
  resetPassword: async (userId, passwordData) => {
    return await api.post(`/users/${userId}/reset-password`, passwordData)
  }
}

export default userService
