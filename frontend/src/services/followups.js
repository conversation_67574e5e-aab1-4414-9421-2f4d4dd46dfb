import api from './api'

export const followupService = {
  // 获取回访记录列表
  getFollowups: async (params = {}) => {
    return await api.get('/followups', { params })
  },

  // 获取回访记录详情
  getFollowup: async (followupId) => {
    return await api.get(`/followups/${followupId}`)
  },

  // 创建回访记录
  createFollowup: async (followupData) => {
    return await api.post('/followups', followupData)
  },

  // 更新回访记录
  updateFollowup: async (followupId, followupData) => {
    return await api.put(`/followups/${followupId}`, followupData)
  },

  // 执行回访
  executeFollowup: async (followupId, data) => {
    return await api.post(`/followups/${followupId}/execute`, data)
  },

  // 删除回访记录
  deleteFollowup: async (followupId) => {
    return await api.delete(`/followups/${followupId}`)
  },

  // 批量创建回访记录预览
  batchPreviewFollowups: async (data) => {
    return await api.post('/followups/batch-preview', data)
  },

  // 批量创建回访记录
  batchCreateFollowups: async (data) => {
    return await api.post('/followups/batch-create-by-template', data)
  }
}

export default followupService
