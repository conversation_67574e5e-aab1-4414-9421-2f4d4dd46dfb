import api from './api'

export const departmentService = {
  // 获取科室列表
  getDepartments: async (params = {}) => {
    return await api.get('/departments', { params })
  },

  // 获取科室详情
  getDepartment: async (deptId) => {
    return await api.get(`/departments/${deptId}`)
  },

  // 创建科室
  createDepartment: async (deptData) => {
    return await api.post('/departments', deptData)
  },

  // 更新科室
  updateDepartment: async (deptId, deptData) => {
    return await api.put(`/departments/${deptId}`, deptData)
  },

  // 删除科室
  deleteDepartment: async (deptId) => {
    return await api.delete(`/departments/${deptId}`)
  },

  // 获取科室用户
  getDepartmentUsers: async (deptId) => {
    return await api.get(`/departments/${deptId}/users`)
  },

  // 获取科室模板
  getDepartmentTemplates: async (deptId) => {
    return await api.get(`/departments/${deptId}/templates`)
  }
}

export default departmentService
