import api from './api'

export const templateService = {
  // 获取模板列表
  getTemplates: async (params = {}) => {
    return await api.get('/templates', { params })
  },

  // 获取模板详情
  getTemplate: async (templateId) => {
    return await api.get(`/templates/${templateId}`)
  },

  // 创建模板
  createTemplate: async (templateData) => {
    return await api.post('/templates', templateData)
  },

  // 更新模板
  updateTemplate: async (templateId, templateData) => {
    return await api.put(`/templates/${templateId}`, templateData)
  },

  // 删除模板
  deleteTemplate: async (templateId) => {
    return await api.delete(`/templates/${templateId}`)
  },

  // 复制模板
  copyTemplate: async (templateId, data) => {
    return await api.post(`/templates/${templateId}/copy`, data)
  }
}

export default templateService
