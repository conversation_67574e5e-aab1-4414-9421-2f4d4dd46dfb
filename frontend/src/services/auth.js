import axios from 'axios'

const authApi = axios.create({
  baseURL: '/auth',
  timeout: 10000,
})

// 响应拦截器
authApi.interceptors.response.use(
  (response) => response.data,
  (error) => {
    const message = error.response?.data?.error || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

// 请求拦截器
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

export const authService = {
  // 登录
  login: async (credentials) => {
    try {
      const response = await authApi.post('/login', credentials)

      // 保存token和用户信息
      if (response.access_token) {
        localStorage.setItem('access_token', response.access_token)
        localStorage.setItem('user', JSON.stringify(response.user))
      }

      return response
    } catch (error) {
      // 清除可能存在的无效token
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      throw error
    }
  },

  // 登出
  logout: () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user')
    window.location.href = '/login'
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    const token = localStorage.getItem('access_token')
    if (!token) {
      throw new Error('未登录')
    }

    try {
      const response = await authApi.get('/profile')
      return response.user
    } catch (error) {
      // 如果获取用户信息失败，清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      throw error
    }
  },

  // 修改密码
  changePassword: async (passwordData) => {
    return await authApi.post('/change-password', passwordData)
  },

  // 更新用户信息
  updateProfile: async (profileData) => {
    return await authApi.put('/update-profile', profileData)
  },

  // 检查是否已登录
  isAuthenticated: () => {
    return !!localStorage.getItem('access_token')
  },

  // 获取本地存储的用户信息
  getLocalUser: () => {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  }
}

export default authService
