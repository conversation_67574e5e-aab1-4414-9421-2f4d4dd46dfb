import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Spin } from 'antd'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Users from './pages/Users'
import Departments from './pages/Departments'
import Templates from './pages/Templates'
import PatientTemplates from './pages/PatientTemplates'
import Patients from './pages/Patients'
import Followups from './pages/Followups'
import TestHistoryInput from './pages/TestHistoryInput'
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'
import ErrorBoundary from './components/ErrorBoundary'
import { authService } from './services/auth'

function App() {
  // 简单检查本地存储中是否有token
  const hasToken = authService.isAuthenticated()

  console.log('App渲染，hasToken:', hasToken)

  return (
    <ErrorBoundary>
      <Routes>
        <Route
          path="/login"
          element={<Login />}
        />

        <Route
          path="/*"
          element={
            <ProtectedRoute isAuthenticated={hasToken}>
              <Layout>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/users" element={<Users />} />
                  <Route path="/departments" element={<Departments />} />
                  <Route path="/templates" element={<Templates />} />
                  <Route path="/patient-templates" element={<PatientTemplates />} />
                  <Route path="/patients" element={<Patients />} />
                  <Route path="/followups" element={<Followups />} />
                  <Route path="/test-history" element={<TestHistoryInput />} />
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </ErrorBoundary>
  )
}

export default App
