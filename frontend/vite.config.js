import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true, // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true
      },
      '/auth': {
        target: 'http://localhost:5001',
        changeOrigin: true
      }
    }
  },
  build: {
    target: 'es2015', // 设置构建目标为ES2015以提高兼容性
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
  optimizeDeps: {
    force: true // 强制重新构建依赖
  }
})
