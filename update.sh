#!/bin/bash

# 🔄 医院回访系统 Linux 更新脚本
# 用于更新医院回访系统到最新版本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 项目目录
PROJECT_DIR="/opt/hospital-followup-system"

# 检查项目目录（兼容旧路径）
if [[ ! -d $PROJECT_DIR ]]; then
    if [[ -d "/opt/hospital-followup" ]]; then
        PROJECT_DIR="/opt/hospital-followup"
        log_info "使用旧项目路径: $PROJECT_DIR"
    fi
fi

# 检查项目目录是否存在
check_project_exists() {
    if [[ ! -d $PROJECT_DIR ]]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先运行部署脚本: ./deploy.sh"
        exit 1
    fi
    log_info "使用项目目录: $PROJECT_DIR"
}

# 备份数据库 (MySQL)
backup_database() {
    log_info "备份MySQL数据库..."

    BACKUP_DIR="$PROJECT_DIR/backups"
    mkdir -p $BACKUP_DIR

    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/mysql_backup_$TIMESTAMP.sql"

    # 从配置文件读取数据库连接信息
    if [[ -f "$PROJECT_DIR/backend/.env" ]]; then
        source "$PROJECT_DIR/backend/.env"
    fi

    # 设置默认值
    MYSQL_HOST=${MYSQL_HOST:-"*************"}
    MYSQL_PORT=${MYSQL_PORT:-"3306"}
    MYSQL_USER=${MYSQL_USER:-"root"}
    MYSQL_PASSWORD=${MYSQL_PASSWORD:-"windows1"}
    MYSQL_DATABASE=${MYSQL_DATABASE:-"hospital_followup"}

    # 使用mysqldump备份数据库
    if command -v mysqldump &> /dev/null; then
        mysqldump -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" > "$BACKUP_FILE"
        if [[ $? -eq 0 ]]; then
            log_success "MySQL数据库备份完成: $BACKUP_FILE"
        else
            log_error "MySQL数据库备份失败"
        fi
    else
        log_warning "未找到mysqldump命令，跳过数据库备份"
        log_info "请手动备份MySQL数据库: $MYSQL_DATABASE"
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."

    # 查找并停止后端进程
    BACKEND_PID=$(ps aux | grep "python.*run.py" | grep -v grep | awk '{print $2}')
    if [[ -n "$BACKEND_PID" ]]; then
        kill $BACKEND_PID
        log_success "后端进程已停止 (PID: $BACKEND_PID)"
    else
        log_info "未找到运行中的后端进程"
    fi

    # 尝试停止systemd服务（如果存在）
    if sudo systemctl is-active --quiet hospital-followup 2>/dev/null; then
        sudo systemctl stop hospital-followup
        log_success "后端服务已停止"
    fi
}

# 更新代码
update_code() {
    log_info "更新代码..."
    
    cd $PROJECT_DIR
    
    # 保存本地修改
    git stash push -m "Auto stash before update $(date)"
    
    # 拉取最新代码
    git pull origin master
    
    log_success "代码更新完成"
}

# 更新后端依赖
update_backend() {
    log_info "更新后端依赖..."

    cd $PROJECT_DIR/backend

    # 检查是否有虚拟环境
    if [[ -d "venv" ]]; then
        source venv/bin/activate
        log_info "已激活虚拟环境"
    else
        log_info "使用系统Python环境"
    fi

    # 更新依赖
    pip3 install -r requirements.txt

    # 数据库迁移
    python3 -c "
from app import create_app, db
app = create_app('production')
app.app_context().push()
db.create_all()
print('数据库更新完成')
"

    log_success "后端更新完成"
}

# 更新前端
update_frontend() {
    log_info "更新前端..."

    cd $PROJECT_DIR/frontend

    # 更新依赖
    npm install

    # 重新构建
    npm run build

    # 复制到nginx目录
    NGINX_DIR="/var/www/html"
    if [[ ! -d "$NGINX_DIR" ]]; then
        sudo mkdir -p "$NGINX_DIR"
    fi

    sudo cp -r dist/* "$NGINX_DIR/"
    sudo chown -R www-data:www-data "$NGINX_DIR"

    log_success "前端更新完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."

    # 启动后端服务
    cd $PROJECT_DIR/backend

    # 检查是否有systemd服务
    if sudo systemctl list-unit-files | grep -q hospital-followup; then
        sudo systemctl daemon-reload
        sudo systemctl start hospital-followup
        log_success "通过systemd启动后端服务"
    else
        # 使用nohup启动后端
        if [[ -d "venv" ]]; then
            source venv/bin/activate
        fi
        nohup python3 run.py > /var/log/hospital-followup-backend.log 2>&1 &
        log_success "通过nohup启动后端服务"
    fi

    # 重新加载nginx
    sudo systemctl reload nginx

    log_success "服务启动完成"
}

# 验证更新
verify_update() {
    log_info "验证更新..."

    # 等待服务启动
    sleep 10

    # 检查后端端口
    if netstat -tlnp | grep -q :5001; then
        log_success "后端服务端口5001正常监听"
    else
        log_warning "后端服务端口5001未监听，检查进程..."
        ps aux | grep "python.*run.py" | grep -v grep || log_error "未找到后端进程"
    fi

    # 检查nginx服务
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务异常"
        sudo systemctl status nginx
        return 1
    fi

    # 测试API
    if curl -s -f http://localhost:5001/auth/login -X POST -H "Content-Type: application/json" -d '{}' > /dev/null 2>&1; then
        log_success "API服务响应正常"
    else
        log_warning "API服务可能需要更多时间启动或需要有效请求"
    fi

    # 测试患者模板API
    if curl -s -f http://localhost:5001/api/patient-templates > /dev/null 2>&1; then
        log_success "患者模板API正常"
    else
        log_warning "患者模板API可能需要认证"
    fi

    log_success "更新验证完成"
}

# 显示更新信息
show_update_info() {
    SERVER_IP=$(curl -s ifconfig.me || echo "localhost")
    DOMAIN_NAME=${DOMAIN_NAME:-"fw.beimoyinhenlinlin.cn"}

    echo ""
    echo "🎉 更新完成！"
    echo ""
    echo "📊 访问信息:"
    echo "  主域名: http://$DOMAIN_NAME"
    echo "  备用IP: http://$SERVER_IP"
    echo "  后端API: http://$DOMAIN_NAME/api"
    echo ""
    echo "✨ 系统功能:"
    echo "  - 用户管理和权限控制"
    echo "  - 科室管理和权限隔离"
    echo "  - 患者信息管理"
    echo "  - 回访模板管理"
    echo "  - 回访记录管理"
    echo "  - 字段录入历史记录"
    echo "  - 智能输入建议"
    echo "  - 数据导出功能"
    echo ""
    echo "🔧 管理命令:"
    echo "  查看后端日志: sudo journalctl -u hospital-followup -f"
    echo "  查找后端进程: ps aux | grep 'python.*run.py'"
    echo "  重启后端: sudo systemctl restart hospital-followup"
    echo "  重启Nginx: sudo systemctl restart nginx"
    echo ""
    echo "📁 备份位置: $PROJECT_DIR/backups/"
    echo ""
    echo "🔑 默认登录:"
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    echo "👥 测试账户:"
    echo "  医生账户: doctor1 / doctor123"
    echo "  护士账户: nurse1 / nurse123"
    echo ""
}

# 回滚功能
rollback() {
    log_warning "开始回滚..."

    # 停止服务
    BACKEND_PID=$(ps aux | grep "python.*run.py" | grep -v grep | awk '{print $2}')
    if [[ -n "$BACKEND_PID" ]]; then
        kill $BACKEND_PID
        log_success "后端进程已停止"
    fi

    # 回滚代码
    cd $PROJECT_DIR
    git stash pop

    # 恢复MySQL数据库（如果有备份）
    LATEST_BACKUP=$(ls -t $PROJECT_DIR/backups/mysql_backup_*.sql 2>/dev/null | head -1)
    if [[ -n $LATEST_BACKUP ]]; then
        log_info "发现MySQL备份文件: $LATEST_BACKUP"
        log_warning "MySQL数据库回滚需要手动执行："
        log_info "mysql -h\$MYSQL_HOST -P\$MYSQL_PORT -u\$MYSQL_USER -p\$MYSQL_PASSWORD \$MYSQL_DATABASE < $LATEST_BACKUP"
        log_info "请根据需要手动执行上述命令"
    else
        log_warning "未找到MySQL备份文件，跳过数据库回滚"
    fi

    # 重新构建前端
    cd $PROJECT_DIR/frontend
    npm run build
    sudo cp -r dist/* /var/www/hospital-followup/

    # 启动服务
    cd $PROJECT_DIR/backend
    nohup python3 run.py > /var/log/hospital-followup-backend.log 2>&1 &

    log_success "回滚完成"
}

# 显示帮助信息
show_help() {
    echo "医院回访系统更新脚本"
    echo ""
    echo "用法:"
    echo "  ./update.sh          - 执行完整更新"
    echo "  ./update.sh rollback - 回滚到上一个版本"
    echo "  ./update.sh help     - 显示此帮助信息"
    echo ""
    echo "更新内容:"
    echo "  - 拉取最新代码"
    echo "  - 备份数据库"
    echo "  - 更新后端依赖"
    echo "  - 重新构建前端"
    echo "  - 数据库迁移"
    echo "  - 重启服务"
    echo ""
    echo "注意事项:"
    echo "  - 确保在 $PROJECT_DIR 目录存在"
    echo "  - 确保有sudo权限"
    echo "  - 更新前会自动备份数据库"
    echo ""
}

# 主函数
main() {
    case "${1:-update}" in
        "rollback")
            echo "🔄 开始回滚..."
            check_project_exists
            rollback
            ;;
        "help")
            show_help
            ;;
        "update"|"")
            echo "🔄 开始更新医院回访系统..."
            echo ""
            
            check_project_exists
            backup_database
            stop_services
            update_code
            update_backend
            update_frontend
            start_services
            verify_update
            show_update_info
            
            log_success "更新完成！"
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
