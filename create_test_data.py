#!/usr/bin/env python3
"""
医院回访系统测试数据创建脚本
用于创建完整的测试数据，包括用户、科室、模板、患者和回访记录
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app import create_app, db
from app.models import (
    User, Department, PatientTemplate, PatientTemplateField, 
    FollowupTemplate, TemplateField, Patient, FollowupRecord, FieldHistory
)
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import json
import random

def create_departments():
    """创建科室数据"""
    print("创建科室数据...")

    departments_data = [
        {'name': '内科', 'code': 'NK', 'description': '内科医疗服务，包括心血管、呼吸、消化等'},
        {'name': '外科', 'code': 'WK', 'description': '外科医疗服务，包括普外、骨外、神外等'},
        {'name': '儿科', 'code': 'EK', 'description': '儿童医疗服务，0-18岁患者'},
        {'name': '妇产科', 'code': 'FCK', 'description': '妇科和产科医疗服务'},
        {'name': '骨科', 'code': 'GK', 'description': '骨科和关节疾病治疗'},
        {'name': '心内科', 'code': 'XNK', 'description': '心血管疾病专科治疗'},
        {'name': '神经内科', 'code': 'SJNK', 'description': '神经系统疾病治疗'},
    ]

    for dept_data in departments_data:
        dept = Department.query.filter_by(name=dept_data['name']).first()
        if not dept:
            dept = Department(**dept_data)
            db.session.add(dept)

    db.session.commit()
    print(f"✓ 创建了 {len(departments_data)} 个科室")

def create_users():
    """创建用户数据"""
    print("创建用户数据...")
    
    users_data = [
        {'username': 'doctor1', 'email': '<EMAIL>', 'password': 'doctor123', 'real_name': '张医生', 'role': 'doctor', 'department': '内科'},
        {'username': 'doctor2', 'email': '<EMAIL>', 'password': 'doctor123', 'real_name': '李医生', 'role': 'doctor', 'department': '外科'},
        {'username': 'doctor3', 'email': '<EMAIL>', 'password': 'doctor123', 'real_name': '王医生', 'role': 'doctor', 'department': '儿科'},
        {'username': 'doctor4', 'email': '<EMAIL>', 'password': 'doctor123', 'real_name': '赵医生', 'role': 'doctor', 'department': '妇产科'},
        {'username': 'nurse1', 'email': '<EMAIL>', 'password': 'nurse123', 'real_name': '陈护士', 'role': 'nurse', 'department': '内科'},
        {'username': 'nurse2', 'email': '<EMAIL>', 'password': 'nurse123', 'real_name': '刘护士', 'role': 'nurse', 'department': '外科'},
        {'username': 'nurse3', 'email': '<EMAIL>', 'password': 'nurse123', 'real_name': '杨护士', 'role': 'nurse', 'department': '儿科'},
        {'username': 'manager1', 'email': '<EMAIL>', 'password': 'manager123', 'real_name': '周主任', 'role': 'dept_admin', 'department': '内科'},
    ]

    for user_data in users_data:
        user = User.query.filter_by(username=user_data['username']).first()
        if not user:
            dept = Department.query.filter_by(name=user_data['department']).first()
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                real_name=user_data['real_name'],
                role=user_data['role'],
                department_id=dept.id if dept else 1
            )
            user.set_password(user_data['password'])
            db.session.add(user)
    
    db.session.commit()
    print(f"✓ 创建了 {len(users_data)} 个用户")

def create_patient_templates():
    """创建患者模板"""
    print("创建患者模板...")
    
    # 获取内科科室
    dept = Department.query.filter_by(name='内科').first()
    if not dept:
        print("⚠ 内科科室不存在，跳过患者模板创建")
        return

    # 标准患者信息模板
    template1 = PatientTemplate.query.filter_by(name='标准患者信息模板').first()
    if not template1:
        template1 = PatientTemplate(
            name='标准患者信息模板',
            description='标准的患者信息录入模板，适用于大部分科室',
            department_id=dept.id,
            created_by=1
        )
        db.session.add(template1)
        db.session.commit()
        
        fields1 = [
            {'field_name': 'name', 'field_label': '患者姓名', 'field_type': 'text', 'is_required': True, 'order': 1},
            {'field_name': 'gender', 'field_label': '性别', 'field_type': 'select', 'field_config': json.dumps({'options': ['男', '女']}), 'is_required': True, 'order': 2},
            {'field_name': 'age', 'field_label': '年龄', 'field_type': 'number', 'is_required': True, 'order': 3},
            {'field_name': 'phone', 'field_label': '联系电话', 'field_type': 'text', 'is_required': True, 'order': 4},
            {'field_name': 'address', 'field_label': '家庭住址', 'field_type': 'textarea', 'is_required': False, 'order': 5},
            {'field_name': 'id_card', 'field_label': '身份证号', 'field_type': 'text', 'is_required': False, 'order': 6},
            {'field_name': 'emergency_contact', 'field_label': '紧急联系人', 'field_type': 'text', 'is_required': False, 'order': 7},
            {'field_name': 'emergency_phone', 'field_label': '紧急联系电话', 'field_type': 'text', 'is_required': False, 'order': 8},
            {'field_name': 'diagnosis', 'field_label': '诊断结果', 'field_type': 'textarea', 'is_required': True, 'order': 9},
            {'field_name': 'treatment', 'field_label': '治疗方案', 'field_type': 'textarea', 'is_required': True, 'order': 10},
            {'field_name': 'medication', 'field_label': '用药情况', 'field_type': 'textarea', 'is_required': False, 'order': 11},
        ]

        for field_data in fields1:
            field = PatientTemplateField(template_id=template1.id, **field_data)
            db.session.add(field)
    
    # 儿科患者模板
    dept_ek = Department.query.filter_by(name='儿科').first()
    template2 = PatientTemplate.query.filter_by(name='儿科患者模板').first()
    if not template2 and dept_ek:
        template2 = PatientTemplate(
            name='儿科患者模板',
            description='专门用于儿科患者的信息模板',
            department_id=dept_ek.id,
            created_by=1
        )
        db.session.add(template2)
        db.session.commit()
        
        fields2 = [
            {'field_name': 'child_name', 'field_label': '患儿姓名', 'field_type': 'text', 'is_required': True, 'order': 1},
            {'field_name': 'gender', 'field_label': '性别', 'field_type': 'select', 'field_config': json.dumps({'options': ['男', '女']}), 'is_required': True, 'order': 2},
            {'field_name': 'birth_date', 'field_label': '出生日期', 'field_type': 'date', 'is_required': True, 'order': 3},
            {'field_name': 'guardian_name', 'field_label': '监护人姓名', 'field_type': 'text', 'is_required': True, 'order': 4},
            {'field_name': 'guardian_phone', 'field_label': '监护人电话', 'field_type': 'text', 'is_required': True, 'order': 5},
            {'field_name': 'relationship', 'field_label': '与患儿关系', 'field_type': 'select', 'field_config': json.dumps({'options': ['父亲', '母亲', '爷爷', '奶奶', '外公', '外婆', '其他']}), 'is_required': True, 'order': 6},
            {'field_name': 'weight', 'field_label': '体重(kg)', 'field_type': 'number', 'is_required': False, 'order': 7},
            {'field_name': 'height', 'field_label': '身高(cm)', 'field_type': 'number', 'is_required': False, 'order': 8},
            {'field_name': 'symptoms', 'field_label': '主要症状', 'field_type': 'textarea', 'is_required': True, 'order': 9},
            {'field_name': 'diagnosis', 'field_label': '诊断结果', 'field_type': 'textarea', 'is_required': True, 'order': 10},
            {'field_name': 'treatment', 'field_label': '治疗方案', 'field_type': 'textarea', 'is_required': True, 'order': 11},
        ]

        for field_data in fields2:
            field = PatientTemplateField(template_id=template2.id, **field_data)
            db.session.add(field)
    
    db.session.commit()
    print("✓ 创建了患者模板")

def create_followup_templates():
    """创建回访模板"""
    print("创建回访模板...")
    
    # 获取内科科室
    dept = Department.query.filter_by(name='内科').first()
    if not dept:
        print("⚠ 内科科室不存在，跳过回访模板创建")
        return

    # 标准回访模板
    template1 = FollowupTemplate.query.filter_by(name='标准回访模板').first()
    if not template1:
        template1 = FollowupTemplate(
            name='标准回访模板',
            description='标准的患者回访模板，适用于大部分回访场景',
            department_id=dept.id,
            created_by=1
        )
        db.session.add(template1)
        db.session.commit()
        
        fields1 = [
            {'field_name': 'health_status', 'field_label': '整体健康状况', 'field_type': 'select', 'field_config': json.dumps({'options': ['很好', '良好', '一般', '较差', '很差']}), 'is_required': True, 'order': 1},
            {'field_name': 'symptoms_improvement', 'field_label': '症状改善情况', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全好转', '明显好转', '略有好转', '无变化', '有所加重']}), 'is_required': True, 'order': 2},
            {'field_name': 'current_symptoms', 'field_label': '当前症状描述', 'field_type': 'textarea', 'is_required': False, 'order': 3},
            {'field_name': 'medication_compliance', 'field_label': '用药依从性', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全按医嘱', '基本按医嘱', '偶尔忘记', '经常忘记', '未按医嘱']}), 'is_required': True, 'order': 4},
            {'field_name': 'side_effects', 'field_label': '药物不良反应', 'field_type': 'textarea', 'is_required': False, 'order': 5},
            {'field_name': 'daily_activities', 'field_label': '日常活动能力', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全正常', '基本正常', '轻度受限', '中度受限', '重度受限']}), 'is_required': True, 'order': 6},
            {'field_name': 'satisfaction', 'field_label': '治疗满意度', 'field_type': 'select', 'field_config': json.dumps({'options': ['非常满意', '满意', '一般', '不满意', '非常不满意']}), 'is_required': True, 'order': 7},
            {'field_name': 'suggestions', 'field_label': '意见建议', 'field_type': 'textarea', 'is_required': False, 'order': 8},
            {'field_name': 'next_appointment', 'field_label': '是否需要复诊', 'field_type': 'select', 'field_config': json.dumps({'options': ['是', '否', '待定']}), 'is_required': True, 'order': 9},
        ]

        for field_data in fields1:
            field = TemplateField(template_id=template1.id, **field_data)
            db.session.add(field)
    
    # 术后回访模板
    dept_wk = Department.query.filter_by(name='外科').first()
    template2 = FollowupTemplate.query.filter_by(name='术后回访模板').first()
    if not template2 and dept_wk:
        template2 = FollowupTemplate(
            name='术后回访模板',
            description='专门用于手术后患者的回访模板',
            department_id=dept_wk.id,
            created_by=1
        )
        db.session.add(template2)
        db.session.commit()
        
        fields2 = [
            {'field_name': 'wound_healing', 'field_label': '伤口愈合情况', 'field_type': 'select', 'field_config': json.dumps({'options': ['愈合良好', '愈合一般', '愈合缓慢', '有感染迹象', '严重感染']}), 'is_required': True, 'order': 1},
            {'field_name': 'pain_level', 'field_label': '疼痛程度(0-10分)', 'field_type': 'select', 'field_config': json.dumps({'options': ['0分(无痛)', '1-2分(轻微)', '3-4分(轻度)', '5-6分(中度)', '7-8分(重度)', '9-10分(剧烈)']}), 'is_required': True, 'order': 2},
            {'field_name': 'mobility', 'field_label': '活动能力', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全正常', '轻度受限', '中度受限', '重度受限', '卧床不起']}), 'is_required': True, 'order': 3},
            {'field_name': 'complications', 'field_label': '并发症情况', 'field_type': 'textarea', 'is_required': False, 'order': 4},
            {'field_name': 'medication_compliance', 'field_label': '术后用药依从性', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全按医嘱', '基本按医嘱', '偶尔忘记', '经常忘记', '未按医嘱']}), 'is_required': True, 'order': 5},
            {'field_name': 'diet_recovery', 'field_label': '饮食恢复情况', 'field_type': 'select', 'field_config': json.dumps({'options': ['完全正常', '基本正常', '食欲不振', '恶心呕吐', '无法进食']}), 'is_required': True, 'order': 6},
            {'field_name': 'satisfaction', 'field_label': '手术满意度', 'field_type': 'select', 'field_config': json.dumps({'options': ['非常满意', '满意', '一般', '不满意', '非常不满意']}), 'is_required': True, 'order': 7},
            {'field_name': 'follow_up_needed', 'field_label': '是否需要复查', 'field_type': 'select', 'field_config': json.dumps({'options': ['是', '否', '待定']}), 'is_required': True, 'order': 8},
        ]

        for field_data in fields2:
            field = TemplateField(template_id=template2.id, **field_data)
            db.session.add(field)
    
    db.session.commit()
    print("✓ 创建了回访模板")

def create_sample_patients():
    """创建示例患者数据"""
    print("创建示例患者数据...")
    
    # 获取模板
    template1 = PatientTemplate.query.filter_by(name='标准患者信息模板').first()
    template2 = PatientTemplate.query.filter_by(name='儿科患者模板').first()
    
    if not template1 or not template2:
        print("⚠ 患者模板不存在，跳过患者数据创建")
        return
    
    # 成人患者数据
    adult_patients = [
        {
            'name': '张三', 'gender': '男', 'age': 45, 'phone': '13800138001',
            'address': '北京市朝阳区建国路1号', 'diagnosis': '高血压、糖尿病',
            'treatment': '降压药物治疗，血糖控制，饮食调节'
        },
        {
            'name': '李四', 'gender': '女', 'age': 38, 'phone': '13800138002',
            'address': '上海市浦东新区陆家嘴路100号', 'diagnosis': '胃炎',
            'treatment': '质子泵抑制剂，饮食调节，定期复查'
        },
        {
            'name': '王五', 'gender': '男', 'age': 52, 'phone': '13800138003',
            'address': '广州市天河区珠江新城', 'diagnosis': '冠心病',
            'treatment': '抗血小板聚集，他汀类药物，生活方式干预'
        },
    ]
    
    for i, patient_data in enumerate(adult_patients):
        patient = Patient(
            template_id=template1.id,
            data=json.dumps(patient_data)
        )
        db.session.add(patient)
    
    # 儿科患者数据
    child_patients = [
        {
            'child_name': '小明', 'gender': '男', 'birth_date': '2018-05-15',
            'guardian_name': '张女士', 'guardian_phone': '13800138004',
            'relationship': '母亲', 'weight': 20, 'height': 110,
            'symptoms': '发热、咳嗽', 'diagnosis': '上呼吸道感染',
            'treatment': '抗病毒治疗，对症处理'
        },
        {
            'child_name': '小红', 'gender': '女', 'birth_date': '2020-03-20',
            'guardian_name': '李先生', 'guardian_phone': '13800138005',
            'relationship': '父亲', 'weight': 15, 'height': 95,
            'symptoms': '腹泻、呕吐', 'diagnosis': '急性胃肠炎',
            'treatment': '补液治疗，饮食调节'
        },
    ]
    
    for i, patient_data in enumerate(child_patients):
        patient = Patient(
            template_id=template2.id,
            data=json.dumps(patient_data)
        )
        db.session.add(patient)
    
    db.session.commit()
    print("✓ 创建了示例患者数据")

def create_sample_followups():
    """创建示例回访记录"""
    print("创建示例回访记录...")
    
    # 获取患者和模板
    patients = Patient.query.all()
    template = FollowupTemplate.query.filter_by(name='标准回访模板').first()
    
    if not patients or not template:
        print("⚠ 患者或回访模板不存在，跳过回访记录创建")
        return
    
    # 为每个患者创建回访记录
    for i, patient in enumerate(patients[:3]):  # 只为前3个患者创建回访
        # 已完成的回访
        completed_followup = FollowupRecord(
            patient_id=patient.id,
            template_id=template.id,
            followup_date=datetime.now() - timedelta(days=7),
            actual_date=datetime.now() - timedelta(days=7),
            followup_method='phone',
            status='completed',
            result='success',
            notes='患者恢复良好，按时服药',
            executor_id=2 + (i % 3)
        )
        db.session.add(completed_followup)
        db.session.flush()  # 获取ID

        # 设置回访数据
        completed_followup.set_data({
            'health_status': '良好',
            'symptoms_improvement': '明显好转',
            'medication_compliance': '完全按医嘱',
            'satisfaction': '满意'
        })

        # 待回访记录
        pending_followup = FollowupRecord(
            patient_id=patient.id,
            template_id=template.id,
            followup_date=datetime.now() + timedelta(days=3),
            followup_method='phone',
            status='pending',
            notes='定期回访'
        )
        db.session.add(pending_followup)
    
    db.session.commit()
    print("✓ 创建了示例回访记录")

def create_field_history():
    """创建字段历史记录"""
    print("创建字段历史记录...")
    
    # 常用的字段值
    history_data = [
        {'field_name': 'diagnosis', 'field_value': '高血压', 'template_type': 'patient', 'user_id': 2},
        {'field_name': 'diagnosis', 'field_value': '糖尿病', 'template_type': 'patient', 'user_id': 2},
        {'field_name': 'diagnosis', 'field_value': '冠心病', 'template_type': 'patient', 'user_id': 2},
        {'field_name': 'treatment', 'field_value': '药物治疗', 'template_type': 'patient', 'user_id': 2},
        {'field_name': 'treatment', 'field_value': '手术治疗', 'template_type': 'patient', 'user_id': 2},
        {'field_name': 'health_status', 'field_value': '良好', 'template_type': 'followup', 'user_id': 2},
        {'field_name': 'satisfaction', 'field_value': '满意', 'template_type': 'followup', 'user_id': 2},
    ]
    
    for data in history_data:
        history = FieldHistory(**data)
        db.session.add(history)
    
    db.session.commit()
    print("✓ 创建了字段历史记录")

def main():
    """主函数"""
    print("🚀 开始创建医院回访系统测试数据...")
    print()
    
    app = create_app()
    app.app_context().push()
    
    try:
        create_departments()
        create_users()
        create_patient_templates()
        create_followup_templates()
        create_sample_patients()
        create_sample_followups()
        create_field_history()
        
        print()
        print("🎉 测试数据创建完成！")
        print()
        print("📊 数据统计:")
        print(f"  科室数量: {Department.query.count()}")
        print(f"  用户数量: {User.query.count()}")
        print(f"  患者模板: {PatientTemplate.query.count()}")
        print(f"  回访模板: {FollowupTemplate.query.count()}")
        print(f"  患者记录: {Patient.query.count()}")
        print(f"  回访记录: {FollowupRecord.query.count()}")
        print()
        print("🔑 测试账户:")
        print("  管理员: admin / admin123")
        print("  医生: doctor1 / doctor123")
        print("  护士: nurse1 / nurse123")
        print("  主任: manager1 / manager123")
        
    except Exception as e:
        print(f"❌ 创建测试数据时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
